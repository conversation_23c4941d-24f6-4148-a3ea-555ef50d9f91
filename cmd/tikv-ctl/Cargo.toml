[package]
name = "tikv-ctl"
version = "0.0.1"
license = "Apache-2.0"
edition = "2018"
publish = false

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine", "cloud-aws", "cloud-gcp", "cloud-azure"]
tcmalloc = ["tikv/tcmalloc"]
jemalloc = ["tikv/jemalloc"]
mimalloc = ["tikv/mimalloc"]
snmalloc = ["tikv/snmalloc"]
portable = ["tikv/portable"]
sse = ["tikv/sse"]
mem-profiling = ["tikv/mem-profiling"]
failpoints = ["tikv/failpoints"]
cloud-aws = [
   "encryption_export/cloud-aws",
   "backup/cloud-aws",
]
cloud-gcp = [
  "encryption_export/cloud-gcp",
  "backup/cloud-gcp",
]
cloud-azure = [
  "encryption_export/cloud-azure",
  "backup/cloud-azure",
]
cloud-storage-grpc = ["backup/cloud-storage-grpc"]
cloud-storage-dylib = ["backup/cloud-storage-dylib"]
test-engine-kv-rocksdb = [
  "tikv/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "tikv/test-engine-raft-raft-engine"
]
test-engines-rocksdb = [
  "tikv/test-engines-rocksdb",
]
test-engines-panic = [
  "tikv/test-engines-panic",
]

nortcheck = ["engine_rocks/nortcheck"]

[dependencies]
backup = { workspace = true }
cdc = { workspace = true }
chrono = "0.4"
clap = "2.32"
collections = { workspace = true }
concurrency_manager = { workspace = true }
crossbeam = "0.8"
encryption_export = { workspace = true }
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
error_code = { workspace = true }
file_system = { workspace = true }
futures = "0.3"
gag = "1.0"
grpcio = { workspace = true }
hex = "0.4"
keys = { workspace = true }
kvproto = { workspace = true }
libc = "0.2"
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
log_wrappers = { workspace = true }
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft-engine-ctl = { git = "https://github.com/tikv/raft-engine.git" }
raft_log_engine = { workspace = true }
raftstore = { workspace = true }
rand = "0.8"
regex = "1"
security = { workspace = true }
serde_json = "1.0"
server = { workspace = true }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
structopt = "0.3"
tempfile = "3.0"
tikv = { workspace = true }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["rt-multi-thread", "time"] }
toml = "0.5"
txn_types = { workspace = true }

[build-dependencies]
cc = "1.0"
time = "0.1"

[target.'cfg(unix)'.dependencies]
signal-hook = "0.3"
