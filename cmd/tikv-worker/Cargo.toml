[package]
name = "tikv-worker"
version = "0.1.0"
edition = "2018"

[dependencies]
chrono = "0.4"
clap = "2.32"
cloud_worker = { workspace = true }
grpcio = { version = "0.10", default-features = false, features = ["openssl-vendored", "protobuf-codec"] }
lazy_static = "1.3"
pd_client = { path = "../../components/pd_client", default-features = false }
prometheus = { version = "0.13", features = ["nightly"] }
security = { path = "../../components/security", default-features = false }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
tikv = { workspace = true }
tikv_util = { path = "../../components/tikv_util" }
toml = "0.5"

[build-dependencies]
time = "0.1"
