[package]
name = "cloud_server"
version = "0.0.1"
license = "Apache-2.0"
edition = "2018"
publish = false

[features]
tcmalloc = ["tikv/tcmalloc"]
jemalloc = ["tikv/jemalloc"]
mimalloc = ["tikv/mimalloc"]
snmalloc = ["tikv/snmalloc"]
portable = ["tikv/portable"]
sse = ["tikv/sse"]
mem-profiling = ["tikv/mem-profiling"]
failpoints = ["fail/failpoints"]
cloud-aws = ["encryption_export/cloud-aws"]
cloud-gcp = ["encryption_export/cloud-gcp"]
test-engines-rocksdb = [
  "tikv/test-engines-rocksdb",
]
test-engines-panic = [
  "tikv/test-engines-panic",
]

nortcheck = ["engine_rocks/nortcheck"]

[dependencies]
api_version = { path = "../api_version" }
async-stream = "0.2"
backtrace = "0.3"
backup = { path = "../backup", default-features = false }
builtin_dfs = { path = "../builtin_dfs" }
bytes = "1.0"
cdc = { path = "../cdc", default-features = false }
chrono = "0.4"
clap = "2.32"
cloud_encryption = { workspace = true }
collections = { path = "../collections" }
concurrency_manager = { path = "../concurrency_manager", default-features = false }
crossbeam = "0.8"
encryption = { path = "../encryption", default-features = false }
encryption_export = { path = "../encryption/export", default-features = false }
engine_rocks = { path = "../engine_rocks", default-features = false }
engine_traits = { path = "../engine_traits", default-features = false }
error_code = { path = "../error_code", default-features = false }
fail = "0.5"
file_system = { path = "../file_system", default-features = false }
flate2 = { version = "1.0", default-features = false, features = ["zlib"] }
fs2 = "0.4"
futures = { version = "0.3.15", features = ["async-await", "compat"] }
futures-timer = "3.0"
grpcio = { version = "0.10", default-features = false, features = ["openssl-vendored", "protobuf-codec"] }
grpcio-health = { version = "0.10", default-features = false, features = ["protobuf-codec"] }
hex = "0.4"
hyper = { version = "0.14", features = ["full"] }
itertools = "0.10"
keys = { path = "../keys", default-features = false }
kvengine = { path = "../kvengine", default-features = false }
kvenginepb = { path = "../kvenginepb" }
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
log_wrappers = { path = "../log_wrappers" }
mime = "0.3.13"
nix = "0.24"
nom = { version = "7.1.0", default-features = false, features = ["std"] }
num_cpus = "1"
online_config = { path = "../online_config" }
openssl = "0.10"
overload_protector = { workspace = true }
paste = "1.0"
pd_client = { path = "../pd_client", default-features = false }
pin-project = "1.0"
pnet_datalink = "0.23"
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
promptly = "0.3.0"
protobuf = "2.8"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft_log_engine = { path = "../raft_log_engine", default-features = false }
raftstore = { path = "../raftstore", default-features = false }
rand = "0.8"
recovery = { workspace = true }
regex = "1.5"
resolved_ts = { path = "../resolved_ts", default-features = false }
resource_metering = { path = "../resource_metering" }
rev_lines = "0.2.1"
rfengine = { path = "../rfengine", default-features = false }
rfenginepb = { workspace = true }
rfstore = { path = "../rfstore", default-features = false }
seahash = "4.1.0"
security = { path = "../security", default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
sst_importer = { path = "../sst_importer" }
sysinfo = "0.26"
tempfile = "3.0"
thiserror = "1.0"
tidb_query_common = { path = "../tidb_query_common" }
tikv = { path = "../..", default-features = false }
tikv_alloc = { path = "../tikv_alloc" }
tikv_kv = { path = "../tikv_kv" }
tikv_util = { path = "../tikv_util", default-features = false }
tokio = { version = "1.12", features = ["full"] }
tokio-openssl = "0.6"
tokio-timer = { git = "https://github.com/tikv/tokio", branch = "tokio-timer-hotfix" }
tokio-util = "0.7"
toml = "0.5"
tonic = "0.9.2"
tracker = { path = "../tracker" }
txn_types = { path = "../txn_types", default-features = false }
url = "2"
uuid = { version = "0.8.1", features = ["serde", "v4"] }
vlog = "0.1.4"
walkdir = "2"
yatp = { git = "https://github.com/tikv/yatp.git", branch = "master" }

[target.'cfg(unix)'.dependencies]
signal-hook = "0.3"
