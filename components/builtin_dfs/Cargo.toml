[package]
name = "builtin_dfs"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
api_version = { workspace = true }
async-trait = "0.1"
bytes = "1.2.1"
http = "0.2"
hyper = { version = "0.14", features = ["full"] }
kvengine = { workspace = true }
kvproto = { workspace = true }
pd_client = { workspace = true }
rand = "0.8"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv-client = { workspace = true }
tikv_util = { path = "../tikv_util", default-features = false }
tokio = { version = "1.5", features = ["full"] }
