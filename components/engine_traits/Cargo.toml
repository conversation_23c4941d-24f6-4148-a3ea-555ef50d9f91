[package]
name = "engine_traits"
version = "0.0.1"
edition = "2018"
publish = false

[features]
failpoints = ["fail/failpoints"]

[dependencies]
bytes = "1.0"
case_macros = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
kvproto = { workspace = true }
log_wrappers = { workspace = true }
protobuf = "2"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
serde = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tracker = { workspace = true }
txn_types = { workspace = true }

[dev-dependencies]
serde_derive = "1.0"
toml = "0.5"
