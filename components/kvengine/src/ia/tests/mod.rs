// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{rc::Rc, sync::Arc, time::Duration};

use crate::{
    engine::Engine,
    ia::{ia_file::IaFile, manager::<PERSON><PERSON><PERSON>anager, util::IaManagerOptionsBuilder},
    table::{file::InMemFile, sstable::SsTable},
    tests::{new_table, new_test_engine_opt},
};

pub struct IaFileTester {
    pub engine: Engine,
    pub manager: IaManager,
    _ia_runtime: tokio::runtime::Runtime,
}

impl IaFileTester {
    pub fn new() -> Self {
        let ia_rt = tokio::runtime::Builder::new_multi_thread()
            .thread_name("ia-mgr")
            .enable_all()
            .worker_threads(2)
            .build()
            .unwrap();
        let (engine, _) = new_test_engine_opt(true, 1024, "");
        let options = IaManagerOptionsBuilder::default()
            .segment_size(4096)
            .sync_read(false, 1, Duration::from_secs(3))
            .build()
            .unwrap();
        let mgr = IaManager::new(options, engine.fs.clone(), None, ia_rt.into()).unwrap();
        Self {
            engine,
            manager: mgr,
            _ia_runtime: ia_rt,
        }
    }

    pub fn new_ia_file(
        &self,
        file_id: u64,
        begin: usize,
        end: usize,
        ver: u64,
        saved_vals: &mut Vec<Rc<Vec<u8>>>,
    ) -> IaFile {
        let t = new_table(&self.engine, file_id, begin, end, ver, false, saved_vals);
        let meta_data = t
            .file()
            .read(
                t.meta_offset() as u64,
                t.size() as usize - t.meta_offset() as usize,
            )
            .unwrap();
        let meta_file = InMemFile::new(t.id(), meta_data);
        IaFile::open_for_sst(t.id(), Arc::new(meta_file), self.manager.clone()).unwrap()
    }
}
