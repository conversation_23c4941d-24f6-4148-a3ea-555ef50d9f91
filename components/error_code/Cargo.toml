[package]
name = "error_code"
version = "0.0.1"
edition = "2018"
publish = false

[lib]
name = "error_code"
path = "src/lib.rs"

[[bin]]
name = "error_code_gen"
path = "bin.rs"

[dependencies]
grpcio = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
serde = { version = "1.0", features = ["derive"] }
tikv_alloc = { workspace = true }
