[package]
name = "causal_ts"
version = "0.0.1"
edition = "2018"
publish = false

[features]
testexport = []

[dependencies]
api_version = { workspace = true }
async-trait = { version = "0.1" }
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
enum_dispatch = "0.3.8"
error_code = { workspace = true }
fail = "0.5"
futures = { version = "0.3" }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
parking_lot = "0.12"
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
serde = "1.0"
serde_derive = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
test_pd_client = { workspace = true }
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1", features = ["sync"] }
txn_types = { workspace = true }

[dev-dependencies]
criterion = "0.3"

[[bench]]
name = "tso"
path = "benches/tso.rs"
harness = false
