[package]
name = "batch-system"
version = "0.1.0"
edition = "2018"

[features]
default = ["test-runner"]
test-runner = ["derive_more"]

[dependencies]
collections = { workspace = true }
crossbeam = "0.8"
derive_more = { version = "0.99", optional = true }
fail = "0.5"
file_system = { workspace = true }
lazy_static = "1.3"
online_config = { workspace = true }
prometheus = { version = "0.13", default-features = false, features = ["nightly"] }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }

[dev-dependencies]
criterion = "0.3"

[[test]]
name = "tests"
path = "tests/cases/mod.rs"
required-features = ["test-runner"]

[[bench]]
name = "router"
path = "benches/router.rs"
harness = false
required-features = ["test-runner"]

[[bench]]
name = "batch-system"
path = "benches/batch-system.rs"
harness = false
required-features = ["test-runner"]
