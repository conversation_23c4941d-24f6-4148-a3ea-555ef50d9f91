[package]
name = "load_data"
version = "0.0.1"
authors = ["The TiKV Authors"]
license = "Apache-2.0"
edition = "2021"
publish = false

[dependencies]
api_version = { path = "../api_version" }
bytes = "1.1"
chrono = "0.4"
cloud_encryption = { workspace = true }
dashmap = "4.0"
encryption = { workspace = true }
futures = "0.3"
hex = "0.4"
http = "0.2"
hyper = { version = "0.14", features = ["full"] }
keys = { workspace = true }
kvengine = { path = "../kvengine" }
kvenginepb = { path = "../../components/kvenginepb" }
kvproto = { workspace = true }
lazy_static = { version = "1.4", features = [] }
pd_client = { path = "../pd_client" }
prometheus = { version = "0.13", features = ["nightly"] }
protobuf = "2.8"
rand = "0.8"
rfengine = { path = "../rfengine" }
rfstore = { path = "../rfstore" }
security = { workspace = true }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_json = "1.0"
slog = { version = "2.3", features = [
    "max_level_trace",
    "release_max_level_debug",
] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tempfile = "3.0"
test_pd_client = { workspace = true }
thiserror = "1.0"
tidb_query_datatype = { path = "../tidb_query_datatype" }
tikv-client = { workspace = true }
tikv_util = { path = "../tikv_util" }
tokio = { version = "1.12", features = ["full"] }

[dev-dependencies]
proptest = "1.4"
