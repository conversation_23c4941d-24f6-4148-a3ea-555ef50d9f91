[package]
name = "merged_engine"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
api_version = { workspace = true }
bytes = "1.2"
cloud_encryption = { workspace = true }
collections = { workspace = true }
crc32fast = "1.4"
file_system = { workspace = true }
kvengine = { workspace = true }
kvenginepb = { workspace = true }
kvproto = { workspace = true }
log = "0.4.19"
native_br = { workspace = true }
pd_client = { workspace = true }
protobuf = { version = "2.8", features = ["bytes"] }
raft-proto = { version = "0.7.0", default-features = false }
resolved_ts = { workspace = true }
rfengine = { workspace = true }
rfenginepb = { workspace = true }
rfstore = { workspace = true }
security = { workspace = true }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
slog = { version = "2.3", features = [
    "max_level_trace",
    "release_max_level_debug",
] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
thiserror = "1.0.44"
tikv = { workspace = true }
tikv_util = { workspace = true }
