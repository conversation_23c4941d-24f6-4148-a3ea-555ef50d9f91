// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

mod error;
mod manifest;
mod preprocessor;

use std::{
    cmp::max,
    collections::{HashMap, HashSet, VecDeque},
    mem,
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};

use api_version::ApiV2;
use bytes::{Buf, BufMut, Bytes};
use cloud_encryption::MasterKey;
pub use error::{Error, Result};
use file_system::{IoRateLimitMode, IoRateLimiter};
use kvengine::{
    dfs::S3Fs, ia::util::IaConfig, limiter::StoreLimiter, MetaIterator, Shard, ShardMeta, TERM_KEY,
};
use kvenginepb::ChangeSet;
use kvproto::{
    metapb,
    metapb::Peer,
    raft_cmdpb::AdminRequest,
    raft_serverpb::{RegionLocalState, StoreIdent},
};
use native_br::common::{
    collect_snapshot_meta_rlog_files, replay_wal_logs_from_backup, ReplayWalLogsContext,
};
use pd_client::PdClient;
use protobuf::Message;
use raft_proto::{eraftpb, eraftpb::Entry};
use rfengine::{
    iterator::WalIterator, RaftLogOp, RfEngine, WriteBatch, RAFT_STATE_KEY_BYTE,
    REGION_META_KEY_PREFIX, TRUNCATE_ALL_INDEX,
};
use rfenginepb::{ClusterBackupMeta, StoreBackupMeta};
use rfstore::{
    store::{
        get_preprocess_cmd,
        state::{RaftApplyState, RaftState},
        write_engine_meta, Applier, ApplyContext, ApplyMsgs, MetaChangeListener, PdIdAllocator,
        PeerMsg, PreprocessContext, PreprocessRef, RecoverHandler, StoreMsg, RAFT_INIT_LOG_INDEX,
    },
    RaftRouter,
};
use security::SecurityConfig;
use serde_derive::{Deserialize, Serialize};
use tikv::config::TikvConfig;
use tikv_util::{
    config::{AbsoluteOrPercentSize, ReadableDuration, ReadableSize},
    info, mpsc, warn,
};

use crate::{manifest::Manifest, preprocessor::Preprocessor};

const RAFT_WRITE_BATCH_SIZE: usize = 4 * 1024 * 1024;

#[derive(Clone)]
pub struct MergedEngineContext {
    pub pd: Arc<dyn PdClient>,
    pub fs: Arc<S3Fs>,
    pub local_dir: PathBuf,
    pub master_key: MasterKey,
    pub config: MergedEngineConfig,
    pub security_config: Arc<SecurityConfig>,
}

#[derive(Clone, Serialize, Deserialize, PartialEq, Debug)]
#[serde(default)]
#[serde(rename_all = "kebab-case")]
pub struct MergedEngineConfig {
    pub block_cache_size: ReadableSize,
    pub timeout_fetch_wal: ReadableDuration,
    pub merged_store_id: u64,
    pub mem_table_size: ReadableSize,
    pub force_ia: bool,
}

impl Default for MergedEngineConfig {
    fn default() -> Self {
        Self {
            block_cache_size: ReadableSize::mb(128),
            timeout_fetch_wal: ReadableDuration::secs(30),
            merged_store_id: 1024,
            mem_table_size: ReadableSize::mb(128),
            force_ia: true,
        }
    }
}

#[derive(Clone, Debug)]
pub struct RegionProgress {
    pub keyspace_id: u32,
    pub entries: HashMap<u64, RaftLogOp>,
    pub synced_index: u64,
    pub commit_index: u64,
    pub truncated_index: u64,
}

impl RegionProgress {
    pub fn new(keyspace_id: u32) -> Self {
        Self {
            keyspace_id,
            entries: HashMap::new(),
            synced_index: 0,
            commit_index: 0,
            truncated_index: 0,
        }
    }
}

#[derive(Clone, Copy, Default, Debug)]
pub struct StoreProgress {
    pub store_id: u64,
    pub epoch: u32,
    pub offset: u64,
}

impl StoreProgress {
    pub(crate) fn encode(&self, buf: &mut Vec<u8>) {
        buf.put_u64_le(self.store_id);
        buf.put_u32_le(self.epoch);
        buf.put_u64_le(self.offset);
    }

    pub(crate) fn decode(buf: &mut impl Buf) -> Result<Self> {
        if (buf.remaining()) < 20 {
            return Err(std::io::Error::new(
                std::io::ErrorKind::UnexpectedEof,
                "store progress buffer is too short",
            )
            .into());
        }
        let store_id = buf.get_u64_le();
        let epoch = buf.get_u32_le();
        let offset = buf.get_u64_le();
        Ok(Self {
            store_id,
            epoch,
            offset,
        })
    }
}

struct EmptyMetaIterator {}

impl MetaIterator for EmptyMetaIterator {
    fn iterate<F>(&mut self, _: F) -> kvengine::Result<()>
    where
        F: FnMut(ChangeSet),
    {
        Ok(())
    }

    fn engine_id(&self) -> u64 {
        0
    }
}

pub struct MergedEngine {
    pub ctx: MergedEngineContext,
    manifest: Manifest,
    region_progresses: HashMap<u64, RegionProgress>,
    updated_regions: HashSet<u64>,
    raft: RfEngine,
    pub kv: kvengine::Engine,
    pub recover_handler: RecoverHandler,
    preprocessors: HashMap<u64, Preprocessor>,
    appliers: HashMap<u64, Applier>,
    peer_receiver: mpsc::Receiver<(u64, Box<PeerMsg>)>,
    _store_receiver: mpsc::Receiver<StoreMsg>, // applier never send store message.
    router: RaftRouter,
}

impl MergedEngine {
    pub fn new(ctx: MergedEngineContext, backup_meta: ClusterBackupMeta) -> Result<Self> {
        let merged_dir = ctx.local_dir.join("merged");
        let merged_cfg = rfengine::RfEngineConfig::default();
        let raft = RfEngine::open(merged_dir.as_path(), &merged_cfg, None, None)?;
        let merged_store_id = ctx.config.merged_store_id;
        if let Some(store_ident) = rfengine::load_store_ident(&raft) {
            if store_ident.get_store_id() != merged_store_id {
                panic!(
                    "store id mismatch, expect {}, got {}",
                    merged_store_id,
                    store_ident.get_store_id()
                );
            }
        } else {
            let mut store_ident = StoreIdent::new();
            store_ident.set_store_id(merged_store_id);
            rfengine::save_store_ident(&raft, &store_ident);
        }
        raft.set_engine_id(merged_store_id);
        let manifest_dir = ctx.local_dir.join("manifest");
        let mut manifest = Manifest::open(&manifest_dir)?;
        let region_progresses = if manifest.store_progresses.is_empty() {
            let (region_progresses, store_progresses) =
                Self::recover_from_backup(&ctx, &backup_meta, &raft)?;
            manifest.store_progresses = store_progresses;
            manifest.persist()?;
            region_progresses
        } else {
            Self::recover_from_merged_raft_engine(&raft)?
        };
        let mut preprocessors = HashMap::new();
        for (region_id, _) in raft.get_region_peer_map() {
            if region_id == 0 {
                continue;
            }
            let processor = Preprocessor::new(&raft, merged_store_id, region_id, &ctx.master_key);
            preprocessors.insert(region_id, processor);
        }
        let io_rate_limiter = Arc::new(IoRateLimiter::new(IoRateLimitMode::WriteOnly, true, true));
        let store_limiter = Arc::new(StoreLimiter::dummy());
        let mut recover_handler = RecoverHandler::new(raft.clone());
        recover_handler.set_merged_engine(true);
        let mut meta_iter = EmptyMetaIterator {};
        let kv = Self::init_kv_engine(
            &ctx,
            io_rate_limiter,
            store_limiter,
            &mut meta_iter,
            recover_handler.clone(),
        )
        .unwrap();
        tikv_util::init_task_local_sync(|| {
            Self::load_shards(
                &ctx,
                &kv,
                recover_handler.clone(),
                &manifest.keyspace_states,
            )
        })?;

        let (store_sender, store_receiver) = mpsc::unbounded();
        let (peer_sender, peer_receiver) = mpsc::unbounded();
        let router = RaftRouter::new(peer_sender, store_sender);
        Ok(Self {
            ctx,
            manifest,
            region_progresses,
            updated_regions: HashSet::new(),
            raft,
            kv,
            recover_handler,
            preprocessors,
            appliers: HashMap::new(),
            _store_receiver: store_receiver,
            peer_receiver,
            router,
        })
    }

    pub fn set_keyspace_states(&mut self, keyspace_id: u32, states: Bytes) -> Result<()> {
        let old = self
            .manifest
            .set_keyspace_states(keyspace_id, states.clone());
        if old == Some(states.clone()) {
            return Ok(()); // no change
        }
        self.manifest.persist()
    }

    pub fn remove_keyspace(&mut self, keyspace_id: u32) {
        self.manifest.keyspace_states.remove(&keyspace_id);
    }

    pub fn get_keyspaces(&self) -> Vec<u32> {
        self.manifest.keyspace_states.keys().cloned().collect()
    }

    pub fn get_keyspace_states(&self, keyspace_id: u32) -> Option<Bytes> {
        self.manifest.keyspace_states.get(&keyspace_id).cloned()
    }

    pub fn get_router(&self) -> RaftRouter {
        self.router.clone()
    }

    pub fn load_shards(
        ctx: &MergedEngineContext,
        kv: &kvengine::Engine,
        mut recoverer: RecoverHandler,
        keyspace_states: &HashMap<u32, Bytes>,
    ) -> Result<()> {
        let engine_id = ctx.config.merged_store_id;
        let mut metas = HashMap::new();
        recoverer.iterate(|cs| {
            let meta = ShardMeta::new(engine_id, &cs);
            if keyspace_states.contains_key(&meta.range.keyspace_id) {
                info!("load keyspace insert cs {:?}", cs);
                metas.insert(meta.id, meta);
            }
        })?;
        kv.load_shards(metas, recoverer, None)?;
        Ok(())
    }

    pub fn close(&self) {
        self.raft.stop_worker(false);
        self.kv.close();
    }

    fn recover_from_backup(
        ctx: &MergedEngineContext,
        backup_meta: &ClusterBackupMeta,
        merged_raft: &RfEngine,
    ) -> Result<(HashMap<u64, RegionProgress>, HashMap<u64, StoreProgress>)> {
        let mut region_progresses = HashMap::new();
        let mut store_progresses = HashMap::new();
        let mut raftdb_pathes = Vec::new();
        for store in backup_meta.get_stores() {
            let store_progress = StoreProgress {
                store_id: store.get_store_id(),
                epoch: store.get_epoch(),
                offset: store.get_offset(),
            };
            store_progresses.insert(store.get_store_id(), store_progress);
            let mut store_config = TikvConfig::default();
            let store_path = ctx.local_dir.join(store.store_id.to_string());
            store_config.storage.data_dir = store_path.to_str().unwrap().to_string();
            store_config.raft_store.raftdb_path =
                store_path.join("raft").to_str().unwrap().to_string();
            store_config.rfengine.lightweight_backup = false;
            let origin = Self::setup_raft_engine(ctx, backup_meta, store).unwrap();
            raftdb_pathes.push(store_config.raft_store.raftdb_path);
            let region_peers_map = origin.get_region_peer_map();
            for (region_id, peer_id) in region_peers_map {
                if region_id == 0 {
                    continue;
                }
                let region_state = rfstore::store::load_last_peer_state(&origin, peer_id).unwrap();
                let keyspace_id =
                    ApiV2::get_u32_keyspace_id_by_key(region_state.get_region().get_start_key())
                        .unwrap_or_default();
                let region_version = region_state.get_region().get_region_epoch().get_version();
                let raft_state =
                    rfstore::store::load_peer_raft_state(&origin, peer_id, region_version).unwrap();
                let preprocess_index = raft_state.get_last_preprocessed_index();
                let region_progress = region_progresses
                    .entry(region_id)
                    .or_insert(RegionProgress::new(keyspace_id));
                if raft_state.get_last_index() > preprocess_index {
                    // Fetch uncommitted entries, and insert them into region progress, so that they
                    // will be replayed when commit index advances (during sync_merged).
                    let mut entry_buf = Vec::new();
                    let low_idx = preprocess_index + 1;
                    let high_idx = raft_state.get_last_index() + 1;
                    if let Err(err) = origin.fetch_raft_entries_to(
                        peer_id,
                        low_idx,
                        high_idx,
                        None,
                        &mut entry_buf,
                    ) {
                        panic!(
                            "fetch raft entries failed for region {}, low: {}, high: {}, err: {}",
                            region_id, low_idx, high_idx, err
                        );
                    }
                    for entry in entry_buf.iter() {
                        if let Some(existing_op) = region_progress.entries.get(&entry.index) {
                            if existing_op.term >= entry.term as u32 {
                                continue;
                            }
                        }
                        region_progress
                            .entries
                            .insert(entry.index, RaftLogOp::new(entry));
                    }
                }
                // Committed entries will be replayed right away during the recovery process
                // below.
                let commit = raft_state.get_commit();
                if region_progress.commit_index >= commit {
                    continue;
                }
                let merged_commit_index = region_progress.commit_index;
                region_progress.commit_index = commit;
                region_progress.synced_index = preprocess_index;
                let truncated_index = max(
                    region_progress.truncated_index,
                    origin
                        .get_truncated_index(peer_id)
                        .unwrap_or(RAFT_INIT_LOG_INDEX),
                )
                .max(RAFT_INIT_LOG_INDEX);
                region_progress.truncated_index = truncated_index;
                // merge states
                let mut batch = rfengine::WriteBatch::new();
                origin.iterate_peer_states(peer_id, false, |k, v| {
                    update_peer_state(&mut batch, k, v, merged_raft.get_engine_id(), region_id);
                    true
                });
                // merge raft logs
                let mut entry_buf = Vec::new();
                let low_idx = merged_commit_index.max(truncated_index) + 1;
                let high_idx = commit + 1;
                if let Err(err) =
                    origin.fetch_raft_entries_to(peer_id, low_idx, high_idx, None, &mut entry_buf)
                {
                    panic!(
                        "fetch raft entries failed for region {}, low: {}, high: {}, err: {}",
                        region_id, low_idx, high_idx, err
                    );
                }
                for entry in entry_buf.iter() {
                    batch.append_raft_log(region_id, region_id, entry);
                }
                batch.truncate_raft_log(region_id, region_id, truncated_index);
                merged_raft.write(batch).unwrap();
            }
        }
        // destroy original raft engines
        for raftdb_path in raftdb_pathes {
            let raft_path = Path::new(&raftdb_path);
            // clean up dir
            std::fs::remove_dir_all(raft_path).unwrap();
        }
        Ok((region_progresses, store_progresses))
    }

    fn recover_from_merged_raft_engine(
        merged_raft: &RfEngine,
    ) -> Result<HashMap<u64, RegionProgress>> {
        let mut region_progersses = HashMap::new();

        // Get region progresses from RfEngine.
        let region_peers_map = merged_raft.get_region_peer_map();
        for (region_id, peer_id) in region_peers_map {
            if region_id == 0 {
                continue;
            }
            let region_state = rfstore::store::load_last_peer_state(merged_raft, peer_id).unwrap();
            let region_version = region_state.get_region().get_region_epoch().get_version();
            let raft_state =
                rfstore::store::load_peer_raft_state(merged_raft, peer_id, region_version).unwrap();
            let commit = raft_state.get_commit();
            let keyspace_id =
                ApiV2::get_u32_keyspace_id_by_key(region_state.get_region().get_start_key())
                    .unwrap_or_default();
            let region_progress = region_progersses
                .entry(region_id)
                .or_insert(RegionProgress::new(keyspace_id));
            region_progress.commit_index = commit;
            region_progress.synced_index = commit;
            region_progress.truncated_index = merged_raft
                .get_truncated_index(region_id)
                .unwrap_or(RAFT_INIT_LOG_INDEX);
        }

        Ok(region_progersses)
    }

    fn setup_raft_engine(
        ctx: &MergedEngineContext,
        backup_meta: &ClusterBackupMeta,
        store: &StoreBackupMeta,
    ) -> Result<RfEngine> {
        let mut store_config = TikvConfig::default();
        let store_path = ctx.local_dir.join(store.store_id.to_string());
        store_config.storage.data_dir = store_path.to_str().unwrap().to_string();
        store_config.raft_store.raftdb_path = store_path.join("raft").to_str().unwrap().to_string();
        store_config.rfengine.lightweight_backup = false;
        let store_id = store.get_store_id();
        let rlog_files = collect_snapshot_meta_rlog_files(
            ctx.fs.clone(),
            &ctx.fs.get_prefix(),
            backup_meta,
            store_id,
        )?;
        rfengine::lightweight_restore(
            store_id,
            None,
            Path::new(&store_config.raft_store.raftdb_path),
            rlog_files.snap_epoch,
            rlog_files.snap_meta,
            rlog_files.snap_rlog,
        )?;
        let raft_db_path = Path::new(&store_config.raft_store.raftdb_path);
        let data_dir = Path::new(&store_config.storage.data_dir);
        let rf_engine = RfEngine::open(raft_db_path, &store_config.rfengine, Some(data_dir), None)?;
        let ctx = ReplayWalLogsContext {
            pd_client: ctx.pd.clone(),
            dfs: ctx.fs.clone(),
            store_id,
            cluster_backup: backup_meta,
            rf_engine: &rf_engine,
            complete_wal_chunks: false,
            full_restore: false,
            fetch_wal_timeout: ctx.config.timeout_fetch_wal.0,
        };
        let tag = &format!("merged_{}", store_id);
        replay_wal_logs_from_backup(tag, &ctx, rlog_files.snap_epoch)?;
        Ok(rf_engine)
    }

    fn init_kv_engine(
        ctx: &MergedEngineContext,
        rate_limiter: Arc<IoRateLimiter>,
        store_limiter: Arc<StoreLimiter>,
        meta_iter: &mut impl kvengine::MetaIterator,
        recoverer: impl kvengine::RecoverHandler + 'static,
    ) -> Result<kvengine::Engine> {
        let kv_engine_path = ctx.local_dir.join("db");
        let mut kv_opts = kvengine::Options::default();
        kv_opts.local_dir = kv_engine_path;
        kv_opts.max_mem_table_size = ctx.config.mem_table_size.0;
        kv_opts.max_block_cache_size = ctx.config.block_cache_size.0 as i64;
        kv_opts.for_restore = true;
        if ctx.config.force_ia {
            kv_opts.ia = IaConfig {
                mem_cap: AbsoluteOrPercentSize::Percent(10.0),
                disk_cap: AbsoluteOrPercentSize::Percent(60.0),
                dynamic_capacity: false,
                force_ia: true,
                ..Default::default()
            };
        }
        let kv_conf = kvengine::KvEngineConfig::default();
        let opts = Arc::new(kv_opts);
        let id_allocator = Arc::new(PdIdAllocator::new(ctx.pd.clone()));
        let (sender, _) = mpsc::unbounded();
        let meta_change_listener = Box::new(MetaChangeListener { sender });
        let kv_engine = kvengine::Engine::open(
            ctx.fs.clone(),
            opts,
            kv_conf,
            meta_iter,
            recoverer,
            id_allocator,
            meta_change_listener,
            rate_limiter,
            store_limiter,
            None,
            ctx.master_key.clone(),
            ctx.pd.get_security_mgr(),
        )?;
        kv_engine.set_engine_id(ctx.config.merged_store_id);
        Ok(kv_engine)
    }

    pub fn get_kv(&self) -> kvengine::Engine {
        self.kv.clone()
    }

    pub fn get_raft(&self) -> RfEngine {
        self.raft.clone()
    }

    pub fn get_region_progress(&self, region_id: u64) -> Option<RegionProgress> {
        self.region_progresses.get(&region_id).cloned()
    }

    pub fn get_store_progress(&self, store_id: u64) -> Option<StoreProgress> {
        self.manifest.store_progresses.get(&store_id).cloned()
    }

    pub fn get_or_insert_store_progress(&mut self, store_id: u64) -> StoreProgress {
        *self
            .manifest
            .store_progresses
            .entry(store_id)
            .or_insert_with(|| StoreProgress {
                store_id,
                epoch: 1,
                offset: 0,
            })
    }

    pub fn get_keyspace_regions(&self, keyspace_id: u32) -> Vec<u64> {
        let mut regions = Vec::new();
        for (&region_id, progress) in &self.region_progresses {
            if progress.keyspace_id == keyspace_id {
                regions.push(region_id);
            }
        }
        regions
    }

    pub fn update_wal(
        &mut self,
        store_id: u64,
        epoch_id: u32,
        offset: u64,
        data: Bytes,
    ) -> Result<()> {
        let cur_offset = if let Some(store_progress) = self.manifest.store_progresses.get(&store_id)
        {
            if store_progress.epoch != epoch_id || store_progress.offset != offset {
                return Err(Error::StoreProgressMismatch(format!(
                    "store {} expect ({}, {}), got ({}, {}), wal_len: {}",
                    store_id,
                    epoch_id,
                    offset,
                    store_progress.epoch,
                    store_progress.offset,
                    data.len(),
                )));
            }
            store_progress.offset
        } else {
            return Err(Error::StoreProgressNotFound(store_id));
        };
        let new_offset = cur_offset + data.len() as u64;
        let mut wal_iterator = WalIterator::new_from_chunks(data, epoch_id, offset);
        let mut origin_batches = Vec::new();
        wal_iterator.iterate_write_batch(|origin_wb| {
            origin_batches.push(origin_wb);
        })?;
        for mut origin_wb in origin_batches {
            let region_peer_map = origin_wb.get_region_peer_map();
            for (&region_id, &peer_id) in &region_peer_map {
                if region_id == 0 {
                    continue;
                }
                let progress = self.region_progresses.entry(region_id).or_insert_with(|| {
                    let bin = origin_wb
                        .get_latest_state(peer_id, region_id, REGION_META_KEY_PREFIX)
                        .unwrap_or_else(|| {
                            panic!("store {} region {} meta not found", store_id, region_id);
                        });
                    let mut region_local_state = RegionLocalState::new();
                    region_local_state.merge_from_bytes(bin).unwrap();
                    let keyspace_id = ApiV2::get_u32_keyspace_id_by_key(
                        region_local_state.get_region().get_start_key(),
                    )
                    .unwrap_or_default();
                    RegionProgress::new(keyspace_id)
                });
                if progress.truncated_index == TRUNCATE_ALL_INDEX {
                    continue;
                }
                if let Some(truncated_idx) = origin_wb.get_truncated_idx(peer_id) {
                    if progress.truncated_index < truncated_idx
                        && truncated_idx != TRUNCATE_ALL_INDEX
                    {
                        progress.truncated_index = truncated_idx;
                    }
                }
                if let Some(v) =
                    origin_wb.get_latest_state(peer_id, region_id, &[RAFT_STATE_KEY_BYTE])
                {
                    if !v.is_empty() {
                        let mut raft_state = RaftState::default();
                        raft_state.unmarshal(v);
                        if raft_state.get_commit() > progress.commit_index {
                            progress.commit_index = raft_state.get_commit();
                        }
                    }
                }
                origin_wb.read_peer_logs(peer_id, |logs| {
                    for log_op in logs {
                        if let Some(existing_op) = progress.entries.get(&log_op.index) {
                            if existing_op.term >= log_op.term {
                                continue;
                            }
                        }
                        progress.entries.insert(log_op.index, log_op.clone());
                    }
                });
                self.updated_regions.insert(region_id);
            }
        }
        self.manifest
            .update_store_progress(store_id, epoch_id, new_offset);
        Ok(())
    }

    pub fn rotate_wal(&mut self, store_id: u64, epoch_id: u32, offset: u64) -> Result<()> {
        if let Some(store_progress) = self.manifest.store_progresses.get_mut(&store_id) {
            if store_progress.epoch != epoch_id || store_progress.offset != offset {
                return Err(Error::StoreProgressMismatch(format!(
                    "store {} expect epoch {}, offset {}, got epoch {}, offset {}",
                    store_id, epoch_id, offset, store_progress.epoch, store_progress.offset
                )));
            }
            info!(
                "rotate store {} at epoch {}, offset {}",
                store_id, epoch_id, offset
            );
            store_progress.epoch += 1;
            store_progress.offset = 0;
        } else {
            return Err(Error::StoreProgressNotFound(store_id));
        }
        Ok(())
    }

    pub fn sync_merged(&mut self, apply_ctx: &mut ApplyContext) -> Result<()> {
        // Prepare context.
        let mut raft_wb = rfengine::WriteBatch::new();
        let mut remove_dependents = Vec::new();
        let mut apply_msgs = ApplyMsgs::default();
        let raft_cfg = rfstore::store::Config::default();
        let mut destroying = HashSet::new();
        let raft_engine = self.raft.clone();
        let router = self.router.clone();
        let mut ctx = PreprocessContext {
            store_id: self.ctx.config.merged_store_id,
            kv: None,
            raft: &raft_engine,
            raft_wb: &mut raft_wb,
            remove_dependents: &mut remove_dependents,
            apply_msgs: &mut apply_msgs,
            cfg: &raft_cfg,
            router: Some(&router),
            destroying: &mut destroying,
        };
        let mut prepared_msgs = HashMap::new();
        let updated_regions: Vec<u64> = self.updated_regions.drain().collect();
        let mut destroyed_regions = HashSet::new();
        self.sync_merged_for_regions(
            &mut ctx,
            apply_ctx,
            &updated_regions,
            &mut destroyed_regions,
            &mut prepared_msgs,
        )?;
        self.handle_prepared_msgs(&mut ctx, prepared_msgs, apply_ctx);
        self.update_progress_and_truncate(&updated_regions, &mut raft_wb);
        self.destroy_regions(destroyed_regions, &mut raft_wb);
        if !raft_wb.is_empty() {
            self.raft.write(raft_wb)?;
        }
        self.manifest.persist()?;
        Ok(())
    }

    fn sync_merged_for_regions(
        &mut self,
        ctx: &mut PreprocessContext<'_>,
        apply_ctx: &mut ApplyContext,
        updated_regions: &[u64],
        destroyed_regions: &mut HashSet<u64>,
        prepared_msgs: &mut HashMap<u64, Vec<(u64, Box<PeerMsg>)>>,
    ) -> Result<()> {
        info!("sync merged for regions {:?}", updated_regions);
        let mut update_queue = VecDeque::from(updated_regions.to_vec());
        let merged_store_id = self.ctx.config.merged_store_id;
        let mut merged_wb = rfengine::WriteBatch::new();
        let mut merged_wb_estimated_size = 0;
        let mut finished_regions = HashSet::new();
        while let Some(updated_region) = update_queue.pop_front() {
            let progress = self.region_progresses.get_mut(&updated_region).unwrap();
            let low = progress.synced_index.max(RAFT_INIT_LOG_INDEX) + 1;
            let high: u64 = progress.commit_index + 1;
            if low >= high {
                finished_regions.insert(updated_region);
                continue;
            }
            if self.raft.get_truncated_index(updated_region).is_none() {
                // region is newly inserted, should process parent first.
                update_queue.push_back(updated_region);
                info!("newly inserted region {}", updated_region);
                continue;
            }
            let preprocessor = self.preprocessors.entry(updated_region).or_insert_with(|| {
                Preprocessor::new(
                    &self.raft,
                    ctx.store_id,
                    updated_region,
                    &self.ctx.master_key,
                )
            });
            let mut preprocessor_ref = preprocessor.as_ref();
            let mut entries = Vec::new();
            let mut postponed = false;
            // preprocess entries.
            for log_index in low..high {
                let mut entry = progress
                    .entries
                    .get(&log_index)
                    .unwrap_or_else(|| {
                        panic!(
                            "entry not found for region {}, log index {}",
                            updated_region, log_index
                        )
                    })
                    .to_entry();
                let mut admin_req = update_entry(&mut entry, merged_store_id);
                if let Some(admin) = admin_req.as_ref() {
                    if admin.has_commit_merge() {
                        let commit_merge = admin.get_commit_merge();
                        if !finished_regions.contains(&commit_merge.get_source().get_id()) {
                            // need to process source region first.
                            update_queue.push_back(updated_region);
                            progress.synced_index = log_index - 1;
                            postponed = true;
                            info!(
                                "{} postponed at {}, low {}, high {}",
                                updated_region, log_index, low, high
                            );
                            break;
                        }
                    }
                }
                let err = preprocessor_ref.preprocess_committed_entry(ctx, &entry);
                if let Some(err) = err {
                    warn!("preprocess committed entry failed"; "region_id" => updated_region, "err" => ?err);
                    // clear failed command.
                    admin_req = None;
                    entry.set_data(Bytes::new());
                }
                preprocessor_ref
                    .raft_state
                    .set_last_preprocessed_index(*preprocessor_ref.preprocessed_index);
                let mut hs = eraftpb::HardState::default();
                hs.set_term(1);
                hs.set_vote(updated_region);
                hs.set_commit(progress.commit_index);
                preprocessor_ref.raft_state.set_hard_state(&hs);
                preprocessor_ref.raft_state.set_last_index(log_index);
                ctx.raft_wb
                    .append_raft_log(updated_region, updated_region, &entry);
                if let Some(admin_req) = admin_req {
                    if admin_req.has_commit_merge() {
                        let last_change_set = ctx.apply_msgs.get_last_change_set();
                        let source = last_change_set.unwrap();
                        destroyed_regions.insert(source.shard_id);
                        ctx.raft
                            .iterate_peer_states(source.shard_id, false, |k, _| {
                                ctx.raft_wb
                                    .set_state(source.shard_id, source.shard_id, k, &[]);
                                true
                            });
                        ctx.raft_wb.truncate_raft_log(
                            source.shard_id,
                            source.shard_id,
                            TRUNCATE_ALL_INDEX,
                        );
                    }
                }
                entries.push(entry);
            }
            if !postponed {
                finished_regions.insert(updated_region);
            }
            if ctx.raft_wb.is_empty() {
                continue;
            }
            preprocessor_ref.write_raft_state(ctx);
            let mut wb = mem::take(ctx.raft_wb);
            ctx.raft.apply(&mut wb);
            merged_wb_estimated_size += wb.estimated_size();
            merged_wb.merge_write_batch(wb);
            if merged_wb_estimated_size > RAFT_WRITE_BATCH_SIZE {
                ctx.raft.persist(merged_wb)?;
                merged_wb = WriteBatch::new();
                merged_wb_estimated_size = 0;
            }
            let shard = self.kv.get_shard(updated_region);
            if shard.is_none() {
                // shard is not in the keyspace range, skip apply.
                continue;
            }
            let shard = shard.unwrap();
            // apply committed entries.
            let applier = self
                .appliers
                .entry(updated_region)
                .or_insert_with(|| Self::new_applier(&shard, preprocessor_ref, low - 1));
            ctx.build_apply_msg_for_replication(entries);
            ctx.handle_apply_msgs_for_replication(applier, apply_ctx);
            // We keep waiting for paused region because later region may depend on it.
            while applier.is_paused() {
                let msgs = if let Some(msgs) = prepared_msgs.remove(&updated_region) {
                    // received by previous region, handle it now.
                    msgs
                } else {
                    let (id, peer_msg) =
                        match self.peer_receiver.recv_timeout(Duration::from_secs(3)) {
                            Ok((id, msg)) => (id, msg),
                            Err(err) => {
                                if err.is_timeout() {
                                    warn!("waiting for region {} to unpause", updated_region);
                                    continue;
                                }
                                return Err(Error::Other(Box::new(err)));
                            }
                        };
                    if id != updated_region {
                        // For another region, handle it later.
                        prepared_msgs.entry(id).or_default().push((id, peer_msg));
                        continue;
                    }
                    vec![(id, peer_msg)]
                };
                Self::apply_prepared_msgs(ctx, applier, msgs, apply_ctx);
            }
            preprocessor.sync_region();
        }
        if !merged_wb.is_empty() {
            self.raft.persist(merged_wb)?;
        }
        Ok(())
    }

    fn handle_prepared_msgs(
        &mut self,
        ctx: &mut PreprocessContext<'_>,
        mut prepared_msgs: HashMap<u64, Vec<(u64, Box<PeerMsg>)>>,
        apply_ctx: &mut ApplyContext,
    ) {
        let msg_count = self.peer_receiver.len();
        for _ in 0..msg_count {
            let (id, peer_msg) = self.peer_receiver.recv().unwrap();
            prepared_msgs.entry(id).or_default().push((id, peer_msg));
        }
        for (region_id, msgs) in prepared_msgs {
            let Some(applier) = self.appliers.get_mut(&region_id) else {
                continue;
            };
            Self::apply_prepared_msgs(ctx, applier, msgs, apply_ctx);
        }
    }

    fn update_progress_and_truncate(&mut self, regions: &[u64], raft_wb: &mut WriteBatch) {
        for &region_id in regions {
            let progress = self.region_progresses.get_mut(&region_id).unwrap();
            let commit_index = progress.commit_index;
            progress.synced_index = commit_index;
            // We need to keep the uncommitted index for the next round.
            progress.entries.retain(|&index, _| index > commit_index);

            let truncated_idx = self.raft.get_truncated_index(region_id).unwrap();
            if progress.truncated_index > truncated_idx {
                if let Some(preprocessor) = self.preprocessors.get_mut(&region_id) {
                    if let Some(shard_meta) = preprocessor.as_ref().shard_meta {
                        if shard_meta.parent.is_some() {
                            // skip truncating region with parent, the parent may need the old
                            // raft logs on recover.
                            continue;
                        }
                        if shard_meta.data_sequence < progress.truncated_index {
                            shard_meta.data_sequence = progress.truncated_index;
                            write_engine_meta(raft_wb, region_id, shard_meta);
                        }
                    }
                }
                raft_wb.truncate_raft_log(region_id, region_id, progress.truncated_index);
            }
        }
    }

    fn destroy_regions(&mut self, destroyed_regions: HashSet<u64>, raft_wb: &mut WriteBatch) {
        for region_id in destroyed_regions {
            self.raft.iterate_peer_states(region_id, false, |k, _| {
                raft_wb.set_state(region_id, region_id, k, &[]);
                true
            });
            raft_wb.truncate_raft_log(region_id, region_id, TRUNCATE_ALL_INDEX);
            self.appliers.remove(&region_id);
            self.kv.remove_shard(region_id);
            self.preprocessors.remove(&region_id);
            let progress = self.region_progresses.get_mut(&region_id).unwrap();
            progress.truncated_index = TRUNCATE_ALL_INDEX;
        }
    }

    fn new_applier(
        shard: &Shard,
        preprocess_ref: PreprocessRef<'_>,
        applied_index: u64,
    ) -> Applier {
        let encryption_key = shard.get_encryption_key();
        let term_val = shard.get_property(TERM_KEY).unwrap();
        let term = term_val.chunk().get_u64_le();
        Applier::new_for_replication(
            preprocess_ref.region.clone(),
            encryption_key,
            RaftApplyState::new(applied_index, term),
        )
    }

    fn apply_prepared_msgs(
        ctx: &mut PreprocessContext<'_>,
        applier: &mut Applier,
        msgs: Vec<(u64, Box<PeerMsg>)>,
        apply_ctx: &mut ApplyContext,
    ) {
        for (_, msg) in msgs {
            ctx.build_prepared_msg_for_replication(msg);
        }
        ctx.handle_apply_msgs_for_replication(applier, apply_ctx);
    }
}

fn update_peer_state(
    wb: &mut rfengine::WriteBatch,
    k: &[u8],
    v: &[u8],
    store_id: u64,
    region_id: u64,
) {
    if k.starts_with(rfengine::REGION_META_KEY_PREFIX) {
        let mut origin_state = kvproto::raft_serverpb::RegionLocalState::new();
        origin_state.merge_from_bytes(v).unwrap();
        let region_version = origin_state.get_region().get_region_epoch().get_version();
        let merged_region_state = merge_region_local_state(&origin_state, store_id);
        let data = merged_region_state.write_to_bytes().unwrap();
        wb.set_state(
            region_id,
            region_id,
            &rfengine::region_state_key(region_version),
            &data,
        );
    } else {
        wb.set_state(region_id, region_id, k, v);
    }
}

// update the entry if needed and return AdminRequest for further processing
// if the entry is admin command.
fn update_entry(entry: &mut Entry, merged_store_id: u64) -> Option<AdminRequest> {
    if entry.get_entry_type() != raft_proto::eraftpb::EntryType::EntryNormal {
        // We don't need to handle conf change, set it to empty.
        entry.set_entry_type(raft_proto::eraftpb::EntryType::EntryNormal);
        entry.set_data(Bytes::new());
        return None;
    }
    if entry.get_data().is_empty() {
        return None;
    }
    let mut cmd = get_preprocess_cmd(entry)?;
    if !cmd.has_admin_request() {
        return None;
    }
    let header = cmd.mut_header();
    let region_id = header.get_region_id();
    let peer = header.mut_peer();
    peer.set_store_id(merged_store_id);
    peer.set_id(region_id);
    let epoch = header.mut_region_epoch();
    epoch.set_conf_ver(1);
    let admin_cmd = cmd.mut_admin_request();
    if admin_cmd.has_splits() {
        let splits = admin_cmd.mut_splits().mut_requests();
        for req in splits.iter_mut() {
            req.set_new_peer_ids(vec![req.new_region_id]);
        }
    } else if admin_cmd.has_prepare_merge() {
        let prepare_merge = admin_cmd.mut_prepare_merge();
        let new_target = merged_region_meta(prepare_merge.get_target(), merged_store_id);
        prepare_merge.set_target(new_target);
    } else if admin_cmd.has_commit_merge() {
        let commit_merge = admin_cmd.mut_commit_merge();
        let new_source = merged_region_meta(commit_merge.get_source(), merged_store_id);
        commit_merge.set_source(new_source);
    }
    let new_cmd = cmd.write_to_bytes().unwrap();
    entry.set_data(new_cmd.into());
    Some(cmd.take_admin_request())
}

// merged region meta has a single peer with id same as region id.
fn merged_region_meta(origin_region: &metapb::Region, store_id: u64) -> metapb::Region {
    let mut merged_peer = Peer::new();
    merged_peer.set_id(origin_region.get_id());
    merged_peer.set_store_id(store_id);
    let mut merged_region = origin_region.clone();
    merged_region.set_peers(vec![merged_peer].into());
    merged_region
}

fn merge_region_local_state(
    origin: &kvproto::raft_serverpb::RegionLocalState,
    store_id: u64,
) -> kvproto::raft_serverpb::RegionLocalState {
    let mut merged = origin.clone();
    merged.set_region(merged_region_meta(origin.get_region(), store_id));
    if merged.has_merge_state() {
        let merge_state = merged.get_merge_state();
        if merge_state.has_target() {
            let new_target = merged_region_meta(merge_state.get_target(), store_id);
            merged.mut_merge_state().set_target(new_target);
        }
    }
    merged
}
