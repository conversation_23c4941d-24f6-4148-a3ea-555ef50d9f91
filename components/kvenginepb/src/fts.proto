// Copyright 2025-present PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package enginepb;

message PackedFileIndexBlock {
  repeated bytes data_block_start_keys = 1;

  // The absolute offset of the data block in the packed file.
  // It contains N+1 data blocks. The last offset is the end of the last data block.
  // Note that data block may contain padding bytes at the beginning.
  // For this reason, do not read data block from the beginning of the offset.
  repeated uint32 data_block_offsets = 2;
}

message PackedFilePropertyBlock {

}
