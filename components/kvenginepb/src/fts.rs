// This file is generated by rust-protobuf 2.8.0. Do not edit
// @generated

// https://github.com/Manishearth/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![cfg_attr(rustfmt, rustfmt_skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unsafe_code)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `fts.proto`

use protobuf::Message as Message_imported_for_functions;
use protobuf::ProtobufEnum as ProtobufEnum_imported_for_functions;

/// Generated files are compatible only with the same version
/// of protobuf runtime.
const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_8_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>ult)]
pub struct PackedFileIndexBlock {
    // message fields
    pub data_block_start_keys: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    pub data_block_offsets: ::std::vec::Vec<u32>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PackedFileIndexBlock {
    fn default() -> &'a PackedFileIndexBlock {
        <PackedFileIndexBlock as ::protobuf::Message>::default_instance()
    }
}

impl PackedFileIndexBlock {
    pub fn new() -> PackedFileIndexBlock {
        ::std::default::Default::default()
    }

    // repeated bytes data_block_start_keys = 1;


    pub fn get_data_block_start_keys(&self) -> &[::std::vec::Vec<u8>] {
        &self.data_block_start_keys
    }
    pub fn clear_data_block_start_keys(&mut self) {
        self.data_block_start_keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_data_block_start_keys(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.data_block_start_keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data_block_start_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.data_block_start_keys
    }

    // Take field
    pub fn take_data_block_start_keys(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.data_block_start_keys, ::protobuf::RepeatedField::new())
    }

    // repeated uint32 data_block_offsets = 2;


    pub fn get_data_block_offsets(&self) -> &[u32] {
        &self.data_block_offsets
    }
    pub fn clear_data_block_offsets(&mut self) {
        self.data_block_offsets.clear();
    }

    // Param is passed by value, moved
    pub fn set_data_block_offsets(&mut self, v: ::std::vec::Vec<u32>) {
        self.data_block_offsets = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data_block_offsets(&mut self) -> &mut ::std::vec::Vec<u32> {
        &mut self.data_block_offsets
    }

    // Take field
    pub fn take_data_block_offsets(&mut self) -> ::std::vec::Vec<u32> {
        ::std::mem::replace(&mut self.data_block_offsets, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for PackedFileIndexBlock {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.data_block_start_keys)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_uint32_into(wire_type, is, &mut self.data_block_offsets)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.data_block_start_keys {
            my_size += ::protobuf::rt::bytes_size(1, &value);
        };
        for value in &self.data_block_offsets {
            my_size += ::protobuf::rt::value_size(2, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.data_block_start_keys {
            os.write_bytes(1, &v)?;
        };
        for v in &self.data_block_offsets {
            os.write_uint32(2, *v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PackedFileIndexBlock {
        PackedFileIndexBlock::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "data_block_start_keys",
                    |m: &PackedFileIndexBlock| { &m.data_block_start_keys },
                    |m: &mut PackedFileIndexBlock| { &mut m.data_block_start_keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "data_block_offsets",
                    |m: &PackedFileIndexBlock| { &m.data_block_offsets },
                    |m: &mut PackedFileIndexBlock| { &mut m.data_block_offsets },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<PackedFileIndexBlock>(
                    "PackedFileIndexBlock",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PackedFileIndexBlock {
        static mut instance: ::protobuf::lazy::Lazy<PackedFileIndexBlock> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PackedFileIndexBlock,
        };
        unsafe {
            instance.get(PackedFileIndexBlock::new)
        }
    }
}

impl ::protobuf::Clear for PackedFileIndexBlock {
    fn clear(&mut self) {
        self.data_block_start_keys.clear();
        self.data_block_offsets.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PackedFileIndexBlock {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.data_block_start_keys, "data_block_start_keys", buf);
        ::protobuf::PbPrint::fmt(&self.data_block_offsets, "data_block_offsets", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for PackedFileIndexBlock {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.data_block_start_keys, "data_block_start_keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.data_block_offsets, "data_block_offsets", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for PackedFileIndexBlock {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PackedFilePropertyBlock {
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PackedFilePropertyBlock {
    fn default() -> &'a PackedFilePropertyBlock {
        <PackedFilePropertyBlock as ::protobuf::Message>::default_instance()
    }
}

impl PackedFilePropertyBlock {
    pub fn new() -> PackedFilePropertyBlock {
        ::std::default::Default::default()
    }
}

impl ::protobuf::Message for PackedFilePropertyBlock {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PackedFilePropertyBlock {
        PackedFilePropertyBlock::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let fields = ::std::vec::Vec::new();
                ::protobuf::reflect::MessageDescriptor::new::<PackedFilePropertyBlock>(
                    "PackedFilePropertyBlock",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PackedFilePropertyBlock {
        static mut instance: ::protobuf::lazy::Lazy<PackedFilePropertyBlock> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PackedFilePropertyBlock,
        };
        unsafe {
            instance.get(PackedFilePropertyBlock::new)
        }
    }
}

impl ::protobuf::Clear for PackedFilePropertyBlock {
    fn clear(&mut self) {
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PackedFilePropertyBlock {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
    }
}
impl ::std::fmt::Debug for PackedFilePropertyBlock {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        Ok(())
    }
}

impl ::protobuf::reflect::ProtobufValue for PackedFilePropertyBlock {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\tfts.proto\x12\x08enginepb\"W\n\x14PackedFileIndexBlock\x12\x1f\n\x15\
    data_block_start_keys\x18\x01\x20\x03(\x0cB\0\x12\x1c\n\x12data_block_of\
    fsets\x18\x02\x20\x03(\rB\0:\0\"\x1b\n\x17PackedFilePropertyBlock:\0B\0b\
    \x06proto3\
";

static mut file_descriptor_proto_lazy: ::protobuf::lazy::Lazy<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::lazy::Lazy {
    lock: ::protobuf::lazy::ONCE_INIT,
    ptr: 0 as *const ::protobuf::descriptor::FileDescriptorProto,
};

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    unsafe {
        file_descriptor_proto_lazy.get(|| {
            parse_descriptor_proto()
        })
    }
}
