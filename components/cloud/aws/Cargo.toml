[package]
name = "aws"
version = "0.0.1"
edition = "2018"
publish = false

[features]
failpoints = ["fail/failpoints"]

[dependencies]
async-trait = "0.1"
base64 = "0.13.0"
bytes = "1.0"
chrono = "0.4"
cloud = { workspace = true }
fail = "0.5"
futures = "0.3"
futures-util = { version = "0.3", default-features = false, features = ["io"] }
# This is only a dependency to vendor openssl for rusoto. It's not clear exactly
# how openssl is built for tikv, but it seems to be controlled by grpcio. This
# makes `cargo test -p aws` link correctly.
grpcio = { workspace = true }
http = "0.2.0"
hyper = "0.14"
hyper-tls = "0.5"
kvproto = { workspace = true }
lazy_static = "1.3"
md5 = "0.7.0"
prometheus = { version = "0.13", default-features = false, features = ["nightly"] }
rusoto_core = "0.46.0"
rusoto_credential = "0.46.0"
rusoto_kms = { version = "0.46.0", features = ["serialize_structs"] }
rusoto_s3 = { version = "0.46.0", features = ["serialize_structs"] }
rusoto_sts = "0.46.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tikv_util = { workspace = true }
# better to not use slog-global, but pass in the logger
tokio = { version = "1.5", features = ["time"] }
url = "2.0"
uuid = { version = "0.8", features = ["v4"] }

[dev-dependencies]
futures = "0.3"
rusoto_mock = "0.46.0"
