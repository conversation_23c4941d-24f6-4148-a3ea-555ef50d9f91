[package]
name = "aliyun"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
async-trait = "0.1"
aws = { path = "../aws" }
base64 = "0.13"
chrono = "0.4"
cloud_encryption = { workspace = true }
http = "0.2"
hyper = "0.14"
hyper-tls = "0.5.0"
rusoto_core = "0.46.0"
rusoto_credential = "0.46.0"
rust-crypto = "^0.2"
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
