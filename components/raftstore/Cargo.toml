[package]
name = "raftstore"
version = "0.0.1"
authors = ["The TiKV Authors"]
license = "Apache-2.0"
edition = "2018"
publish = false

[features]
default = [
  "test-engine-kv-rocksdb",
  "test-engine-raft-raft-engine",
  "engine_rocks",
]
failpoints = ["fail/failpoints"]
testexport = []
test-engine-kv-rocksdb = ["engine_test/test-engine-kv-rocksdb"]
test-engine-raft-raft-engine = ["engine_test/test-engine-raft-raft-engine"]
test-engines-rocksdb = ["engine_test/test-engines-rocksdb"]
test-engines-panic = ["engine_test/test-engines-panic"]

cloud-aws = ["sst_importer/cloud-aws"]
cloud-gcp = ["sst_importer/cloud-gcp"]
cloud-azure = ["sst_importer/cloud-azure"]

[dependencies]
batch-system = { workspace = true }
bitflags = "1.0.1"
byteorder = "1.2"
bytes = "1.0"
causal_ts = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
crc32fast = "1.2"
crossbeam = "0.8"
derivative = "2"
encryption = { workspace = true }
engine_rocks = { workspace = true, optional = true }

# Should be [dev-dependencies] but we need to control the features
# https://github.com/rust-lang/cargo/issues/6915
engine_test = { workspace = true }
engine_traits = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
fs2 = "0.4"
futures = "0.3"
futures-util = { version = "0.3.1", default-features = false, features = [
  "io",
] }
getset = "0.1"
grpcio-health = { version = "0.10", default-features = false, features = [
  "protobuf-codec",
] }
into_other = { workspace = true }
itertools = "0.10"
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log = { version = "0.4", features = [
  "max_level_trace",
  "release_max_level_debug",
] }
log_wrappers = { workspace = true }
memory_trace_macros = { workspace = true }
online_config = { workspace = true }
openssl = "0.10"
ordered-float = "2.6"
parking_lot = "0.12"
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = [
  "protobuf-codec",
] }
raft-proto = { version = "0.7.0", default-features = false }
rand = "0.8.3"
resource_metering = { workspace = true }
security = { workspace = true }
serde = "1.0"
serde_derive = "1.0"
serde_with = "1.4"
slog = { version = "2.3", features = [
  "max_level_trace",
  "release_max_level_debug",
] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
smallvec = "1.4"
sst_importer = { workspace = true }
tempfile = "3.0"
thiserror = "1.0"
tidb_query_datatype = { workspace = true }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
time = "0.1"
tokio = { version = "1.5", features = ["sync", "rt-multi-thread"] }
tracker = { workspace = true }
txn_types = { workspace = true }
uuid = { version = "0.8.1", features = ["serde", "v4"] }
yatp = { workspace = true }

[dev-dependencies]
encryption_export = { workspace = true }
engine_panic = { workspace = true }
engine_rocks = { workspace = true }
panic_hook = { workspace = true }
test_sst_importer = { workspace = true }
