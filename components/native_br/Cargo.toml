[package]
name = "native_br"
version = "0.0.1"
authors = ["The TiKV Authors"]
license = "Apache-2.0"
edition = "2021"
publish = false

[features]
pprof-fp = ["tikv/pprof-fp"]
testexport = []
failpoints = ["fail/failpoints", "tikv/failpoints"]

[dependencies]
ahash = "0.8.11"
api_version = { workspace = true }
bstr = "0.2"
byteorder = "1.2"
bytes = "1.0"
chrono = "0.4"
cloud_encryption = { workspace = true }
cloud_server = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
dashmap = "4.0"
engine_traits = { workspace = true }
etcd-client = { git = "https://github.com/pingcap/etcd-client", rev = "14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e", features = ["pub-response-field", "tls-openssl-vendored"] }
fail = "0.5"
file_system = { workspace = true }
futures = "0.3"
grpcio = { version = "0.10", default-features = false, features = ["openssl-vendored", "protobuf-codec"] }
hex = "0.4"
http = "0.2.8"
hyper = "0.14"
itertools = "0.10"
kvengine = { workspace = true }
kvenginepb = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
protobuf = "2.8"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
regex = "1"
rfengine = { workspace = true }
rfenginepb = { workspace = true }
rfstore = { workspace = true }
security = { workspace = true }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
tempdir = "0.3"
thiserror = "1.0"
tikv = { workspace = true }
tikv-client = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.12", features = ["full"] }
txn_types = { workspace = true }
url = "2"

[dev-dependencies]
tempfile = "3.0"
test_cloud_server = { workspace = true }
test_pd_client = { workspace = true }
test_util = { workspace = true }
