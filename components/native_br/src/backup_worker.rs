// Copyright 2023 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    fmt,
    sync::Arc,
    thread,
    time::{Duration, SystemTime, UNIX_EPOCH},
};

use futures::executor::block_on;
use pd_client::PdClient;
use rfenginepb::ClusterBackupMeta;
use tikv_util::{
    config::ReadableDuration,
    error, info,
    retry::try_wait_result_async,
    time::Instant,
    warn,
    worker::{<PERSON>zy<PERSON>orker, Runnable, RunnableWithTimer, Scheduler},
};

use crate::{
    backup,
    backup::{
        update_service_safe_point, BackupConfig, BackupType, IncrementalBackupFile, Result,
        SharedResult,
    },
    error::{Error, SharedError},
    metrics::{NATIVE_BR_BACKUP_ERROR, NATIVE_BR_BACKUP_SUCCESS},
};

pub const DEFAULT_TIMEOUT_INSTANT_BACKUP: ReadableDuration = ReadableDuration::secs(60);
const MIN_BACKUP_INTERVAL: Duration = Duration::from_millis(1050);

type InstantBackupCallback = Box<dyn FnOnce(SharedResult<Arc<IncrementalBackupFile>>) + Send>;

enum BackupTask {
    LightweightBackup {
        cb: InstantBackupCallback,
    },
    BackupResult {
        backup_ts: u64,
        res: Result<(String, ClusterBackupMeta)>,
        cb: Option<InstantBackupCallback>,
    },
}

impl fmt::Display for BackupTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            BackupTask::LightweightBackup { .. } => write!(f, "lightweight backup"),
            BackupTask::BackupResult { .. } => write!(f, "backup result"),
        }
    }
}

pub struct BackupWorker {
    worker: LazyWorker<BackupTask>,
    scheduler: Scheduler<BackupTask>,
}

impl BackupWorker {
    pub fn new(
        config: BackupConfig,
        pd_client: Arc<dyn PdClient>,
        backup_interval: Duration,
    ) -> Self {
        let mut worker = LazyWorker::new("backup-worker");
        let scheduler = worker.scheduler();
        let runner = BackupRunner::new(config, pd_client, backup_interval, scheduler.clone());
        runner.init();
        let ok = if runner.periodic_backup_enabled() {
            worker.start_with_timer(runner)
        } else {
            worker.start(runner)
        };
        assert!(ok);
        Self { worker, scheduler }
    }

    pub fn stop(&mut self) {
        self.worker.stop();
    }

    pub async fn instant_backup(&self) -> Result<Arc<IncrementalBackupFile>> {
        let (cb, fut) = tikv_util::future::paired_future_callback();
        self.scheduler
            .schedule(BackupTask::LightweightBackup { cb })
            .unwrap();
        fut.await.unwrap().map_err(|e| {
            warn!("instant backup failed"; "error" => ?e);
            e.into()
        })
    }

    pub async fn instant_backup_with_retry(
        &self,
        timeout: Duration,
    ) -> Result<Arc<IncrementalBackupFile>> {
        // TODO: retry only when the error is retryable
        try_wait_result_async(
            || Box::pin(self.instant_backup()),
            timeout,
            || Duration::from_millis(500),
        )
        .await
    }
}

struct BackupRunner {
    config: BackupConfig,
    pd_client: Arc<dyn PdClient>,
    backup_interval: Duration,
    last_backup_time: Instant,

    last_backup_ts: u64,
    last_backup_meta: Option<ClusterBackupMeta>,
    // Backup ts of all backup tasks.
    tasks_backup_ts: Vec<u64>,

    scheduler: Scheduler<BackupTask>,
}

impl BackupRunner {
    fn new(
        config: BackupConfig,
        pd_client: Arc<dyn PdClient>,
        backup_interval: Duration,
        scheduler: Scheduler<BackupTask>,
    ) -> Self {
        Self {
            config,
            pd_client,
            backup_interval,
            last_backup_time: Instant::now() - MIN_BACKUP_INTERVAL,
            last_backup_ts: 0,
            last_backup_meta: None,
            tasks_backup_ts: vec![],
            scheduler,
        }
    }

    fn init(&self) {
        if self.periodic_backup_enabled() {
            match Self::init_service_safe_point(self.pd_client.as_ref()) {
                Ok(safe_point) => {
                    info!("backup worker: init service safe point: {}", safe_point);
                }
                Err(e) => {
                    warn!("backup worker: init service safe point failed"; "err" => ?e);
                }
            }
        }
    }

    fn init_service_safe_point(pd_client: &dyn PdClient) -> Result<u64> {
        let gc_safe_point = block_on(pd_client.get_gc_safe_point())?;
        match update_service_safe_point(pd_client, gc_safe_point) {
            Ok(()) => Ok(gc_safe_point),
            Err(Error::PdError(pd_client::Error::UnsafeServiceGcSafePoint {
                requested,
                current_minimal,
            })) => {
                debug_assert_eq!(requested, gc_safe_point.into());
                info!("backup worker: service safe point has been set"; "current" => ?current_minimal);
                Ok(current_minimal.into_inner())
            }
            Err(e) => Err(e),
        }
    }

    fn handle_periodic_backup(&mut self) {
        debug_assert!(self.periodic_backup_enabled());
        if self.last_backup_time.saturating_elapsed() < MIN_BACKUP_INTERVAL {
            return;
        }

        self.do_lightweight_backup_inner(None);
    }

    fn do_lightweight_backup(&mut self, cb: InstantBackupCallback) {
        if self.last_backup_time.saturating_elapsed() < MIN_BACKUP_INTERVAL {
            // The backup is named according to the seconds of TSO physical time.
            // So sleep for more than 1 second to avoid the name conflict.
            thread::sleep(MIN_BACKUP_INTERVAL);
        }

        self.do_lightweight_backup_inner(Some(cb));
    }

    fn prepare_backup(&self) -> Result<u64 /* backup_ts */> {
        backup::get_backup_ts(self.pd_client.as_ref()).map_err(Into::into)
    }

    fn do_lightweight_backup_inner(&mut self, cb: Option<InstantBackupCallback>) {
        let backup_ts = match self.prepare_backup() {
            Ok(backup_ts) => backup_ts,
            Err(err) => {
                error!("backup worker: prepare backup failed"; "err" => ?err);
                NATIVE_BR_BACKUP_ERROR.inc();
                if let Some(cb) = cb {
                    cb(Err(SharedError::from(err)));
                }
                return;
            }
        };

        self.last_backup_time = Instant::now();
        if self.periodic_backup_enabled() {
            self.tasks_backup_ts.push(backup_ts);
        }

        let config = self.config.clone();
        let pd_client = self.pd_client.clone();
        let last_backup_meta = self.last_backup_meta.clone();
        let scheduler = self.scheduler.clone();
        thread::spawn(move || {
            info!("backup worker: start backup"; "backup_ts" => backup_ts, "delay" => ?config.backup_delay);
            thread::sleep(config.backup_delay.0);
            let res = backup::backup_cluster_with_ts(
                config,
                BackupType::Lightweight,
                "".to_string(),
                pd_client.as_ref(),
                backup_ts,
                last_backup_meta,
            );
            scheduler.schedule(BackupTask::BackupResult { backup_ts, res, cb })
        });
    }

    fn handle_backup_result(
        &mut self,
        backup_ts: u64,
        res: Result<(String, ClusterBackupMeta)>,
        cb: Option<InstantBackupCallback>,
    ) {
        match res {
            Ok((backup_path, backup_meta)) => {
                info!("backup succeeded"; "path" => ?backup_path, "meta" => %backup_meta);
                if self.periodic_backup_enabled() {
                    if self.last_backup_ts < backup_ts {
                        self.last_backup_ts = backup_ts;
                        self.last_backup_meta = Some(backup_meta);
                    }

                    self.try_update_service_safe_point(backup_ts);
                }

                NATIVE_BR_BACKUP_SUCCESS.inc();
                if let Some(cb) = cb {
                    cb(Ok(Arc::new(
                        IncrementalBackupFile::try_from_full_path(&backup_path).unwrap(),
                    )))
                }
            }
            Err(err) => {
                NATIVE_BR_BACKUP_ERROR.inc();
                if let Some(cb) = cb {
                    cb(Err(SharedError::from(err)));
                }
            }
        }

        if self.periodic_backup_enabled() {
            self.tasks_backup_ts.retain(|&ts| ts != backup_ts);
        }
    }

    fn try_update_service_safe_point(&mut self, backup_ts: u64) {
        if self
            .tasks_backup_ts
            .first()
            .is_some_and(|&ts| ts == backup_ts)
        {
            // Update the service safe point only for the earliest backup task.
            // Otherwise, the backup ts of earlier tasks will be behind the service safe
            // point.
            if let Err(err) = update_service_safe_point(self.pd_client.as_ref(), backup_ts) {
                error!("backup worker: update safepoint failed"; "err" => ?err);
                NATIVE_BR_BACKUP_ERROR.inc();
            }
        }
    }

    fn periodic_backup_enabled(&self) -> bool {
        !self.backup_interval.is_zero()
    }
}

impl Runnable for BackupRunner {
    type Task = BackupTask;

    fn run(&mut self, task: BackupTask) {
        match task {
            BackupTask::LightweightBackup { cb } => {
                self.do_lightweight_backup(cb);
            }
            BackupTask::BackupResult { backup_ts, res, cb } => {
                self.handle_backup_result(backup_ts, res, cb);
            }
        }
    }
}

impl RunnableWithTimer for BackupRunner {
    fn on_timeout(&mut self) {
        self.handle_periodic_backup();
    }

    fn get_interval(&self) -> Duration {
        let mut interval = self.backup_interval.as_secs();
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default();
        interval -= now.as_secs() % interval;
        Duration::from_secs(interval)
    }
}

#[cfg(test)]
mod tests {
    use test_pd_client::TestPdClient;

    use super::*;

    #[test]
    fn test_init_service_safe_point() {
        test_util::init_log_for_test();

        let pd_client = TestPdClient::new(1, false);
        pd_client.set_bootstrap(true);
        pd_client.set_gc_safe_point(1000).unwrap();

        assert_eq!(
            BackupRunner::init_service_safe_point(&pd_client).unwrap(),
            1000
        );

        update_service_safe_point(&pd_client, 2000).unwrap();
        // Will try to init with 1000.
        assert_eq!(
            BackupRunner::init_service_safe_point(&pd_client).unwrap(),
            2000
        );
    }
}
