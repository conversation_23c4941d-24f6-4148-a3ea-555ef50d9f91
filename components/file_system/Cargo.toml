[package]
name = "file_system"
version = "0.1.0"
edition = "2018"
publish = false

[features]
bcc-iosnoop = ["bcc"]

[dependencies]
collections = { workspace = true }
crc32fast = "1.2"
crossbeam-utils = "0.8.0"
fs2 = "0.4"
lazy_static = "1.3"
libc = "0.2"
online_config = { workspace = true }
openssl = "0.10"
parking_lot = "0.12"
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
rand = "0.8"
serde = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
strum = { version = "0.20", features = ["derive"] }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["time"] }

[dev-dependencies]
maligned = "0.2.1"
tempfile = "3.0"

[target.'cfg(target_os = "linux")'.dependencies]
bcc = { version = "0.0.30", optional = true }
thread_local = "1.1.3"
