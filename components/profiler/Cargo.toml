[package]
name = "profiler"
version = "0.0.1"
edition = "2018"
publish = false

[features]
profiling = ["lazy_static", "gperftools", "callgrind", "valgrind_request"]

[dependencies]
tikv_alloc = { workspace = true }

[target.'cfg(unix)'.dependencies]
lazy_static = { version = "1.3.0", optional = true }
gperftools = { version = "0.2.0", optional = true }
callgrind = { version = "1.1.0", optional = true }
valgrind_request = { version = "1.1.0", optional = true }

[[example]]
name = "prime"
path = "examples/prime.rs"
required-features = ["profiling"]
