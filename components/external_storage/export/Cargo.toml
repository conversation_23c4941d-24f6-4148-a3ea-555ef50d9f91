[package]
name = "external_storage_export"
version = "0.0.1"
edition = "2018"
publish = false

[[bin]]
name = "tikv-cloud-storage"
path = "src/bin/tikv-cloud-storage.rs"
required-features = ["cloud-storage-grpc"]

[lib]
name = "external_storage_export"
# Experimental feature to load the cloud storage code dynamically
# crate-type = ["lib", "cdylib"]

[features]
default = ["cloud-gcp", "cloud-aws", "cloud-azure"]
cloud-aws = ["aws"]
cloud-gcp = ["gcp"]
cloud-azure = ["azure"]
cloud-storage-dylib = [
  "external_storage/cloud-storage-dylib",
  "ffi-support",
  "file_system",
  "futures",
  "libloading",
  "lazy_static",
  "once_cell",
  "protobuf",
  "slog",
  "slog-global",
  "tokio",
  "tokio-util",
]
cloud-storage-grpc = [
  "external_storage/cloud-storage-grpc",
  "grpcio",
  "file_system",
  "futures",
  "futures-executor",
  "libc",
  "signal-hook",
  "slog",
  "slog-global",
  "slog-term",
  "tokio",
  "tokio-util",
]

[dependencies]
async-compression = { version = "0.3.14", features = ["futures-io", "zstd"] }
async-trait = "0.1"
aws = { optional = true, workspace = true }
azure = { optional = true, workspace = true }
cloud = { workspace = true }
encryption = { workspace = true }
engine_traits = { workspace = true }
external_storage = { workspace = true }
ffi-support = { optional = true, version = "0.4.2" }
file_system = { workspace = true, optional = true }
futures = { optional = true, version = "0.3" }
futures-executor = { optional = true, version = "0.3" }
futures-io = { version = "0.3" }
futures-util = { version = "0.3", default-features = false, features = ["io"] }
gcp = { optional = true, workspace = true }
grpcio = { workspace = true, optional = true }
kvproto = { workspace = true }
lazy_static = { optional = true, version = "1.3" }
libloading = { optional = true, version = "0.7.0" }
once_cell = { optional = true, version = "1.3.1" }
protobuf = { optional = true, version = "2" }
slog-global = { optional = true, version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["time", "rt", "net"], optional = true }
tokio-util = { version = "0.7", features = ["compat"], optional = true }
url = "2.0"

[dev-dependencies]
futures-util = { version = "0.3", default-features = false, features = ["io"] }
matches = "0.1.8"
rust-ini = "0.14.0"
structopt = "0.3"
tempfile = "3.1"
tokio = { version = "1.5", features = ["time"] }

[[example]]
name = "scli"
path = "examples/scli.rs"

[target.'cfg(unix)'.dependencies]
nix = { optional = true, version = "0.24" }
signal-hook = { optional = true, version = "0.3" }
libc = { optional = true, version = "0.2" }
slog = { optional = true, version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-term = { optional = true, version = "2.4" }
