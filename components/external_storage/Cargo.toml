[package]
name = "external_storage"
version = "0.0.1"
edition = "2018"
publish = false

[features]
cloud-storage-dylib = [
  "ffi-support",
  "libloading",
  "protobuf",
]
cloud-storage-grpc = [
  "grpcio",
]
failpoints = ["fail/failpoints"]

[dependencies]
async-compression = { version = "0.3.14", features = ["futures-io", "zstd"] }
async-trait = "0.1"
bytes = "1.0"
encryption = { workspace = true }
engine_traits = { workspace = true }
fail = "0.5"
ffi-support = { optional = true, version = "0.4.2" }
file_system = { workspace = true }
futures = "0.3"
futures-executor = "0.3"
futures-io = "0.3"
futures-util = { version = "0.3", default-features = false, features = ["io"] }
grpcio = { workspace = true, optional = true }
kvproto = { workspace = true }
lazy_static = "1.3"
libloading = { optional = true, version = "0.7.0" }
openssl = "0.10"
prometheus = { version = "0.13", default-features = false, features = ["nightly", "push"] }
protobuf = { optional = true, version = "2" }
rand = "0.8"
rusoto_core = "0.46.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
# better to not use slog-global, but pass in the logger
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["time", "fs", "process"] }
tokio-util = { version = "0.7", features = ["compat"] }
url = "2.0"

[dev-dependencies]
matches = "0.1.8"
rust-ini = "0.14.0"
structopt = "0.3"
tempfile = "3.1"
tokio = { version = "1.5", features = ["macros"] }
