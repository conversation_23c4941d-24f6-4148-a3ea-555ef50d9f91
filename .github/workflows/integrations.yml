name: Integrations

on:
  pull_request:
  workflow_dispatch:

# Concurrency ensures that only a single job or workflow using the same concurrency group will run at a time.
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  dev:
    name: Run integrations tests
    runs-on: [amd64, ci]
    env:
      RUSTFLAGS: -Dwarnings
      FAIL_POINT: "1"
      ROCKSDB_SYS_SSE: "1"
      RUST_BACKTRACE: "1"
      LOG_LEVEL: INFO
      EXTRA_CARGO_ARGS: --no-run
      CARGO_UNSTABLE_SPARSE_REGISTRY: true
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
      - name: Check cmake & protoc # use local to work around github rate limit
        run: |
          cmake --version
          protoc --version
      - name: Configure git for private modules
        env:
          TOKEN: ${{ secrets.GIT_ACTION_BOT }}
        run: git config --global url."https://${TOKEN}@github.com/".insteadOf "https://github.com/"
      - name: Install `rust` toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2023-12-10
      - name: Set crates.io mirror # https://developer.aliyun.com/mirror/crates.io-index
        run: |
          mkdir -vp ${CARGO_HOME:-$HOME/.cargo}
          cat << EOF | tee ${CARGO_HOME:-$HOME/.cargo}/config.toml
          [source.crates-io]
          replace-with = 'aliyun'
          [source.aliyun]
          registry = "sparse+https://mirrors.aliyun.com/crates.io-index/"
          EOF
      - name: Run integrations tests
        env:
          CARGO_INCREMENTAL: "0"
        working-directory: ./
        run: |
          make test-cloud-engine-integration
