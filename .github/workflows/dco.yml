name: DCO
on:
  pull_request:
  push:
    branches:
      - cloud-engine

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
      - name: Setup Python 3.x
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'

      - name: Check DCO
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          pip3 install -U dco-check
          dco-check --verbose