name: CD

on:
  push:
    branches:
      - cloud-engine
  workflow_dispatch:
    inputs:
      update_daily_run_image:
        description: "update daily-run image"
        type: boolean
        default: false
      daily_run_image_tag:
        description: "daily-run image tag"
        type: string
        default: "v7.5.0"

jobs:
  build-arm64:
    name: Build ARM64 Image
    runs-on: arm64
    timeout-minutes: 120
    steps:
      - name: Check out Code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
      - name: Login to Harbor
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: ${{ secrets.HARBOR_USER }}
          password: ${{ secrets.HARBOR_TOKEN }}
      - name: Build Image Harbor
        uses: docker/build-push-action@v2
        with:
          platforms: "linux/arm64"
          push: true
          # To disable the unnecessary git pull
          context: .
          build-args: |
            GIT_HASH=${{ github.sha }}
            GIT_BRANCH=${{ github.ref }}
            GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}
          tags: |
            hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}-arm64
  build-amd64:
    name: Build AMD64 Image
    runs-on: [amd64, cd]
    timeout-minutes: 120
    outputs:
      image: tikv-cse:${{ github.sha }}-amd64
    steps:
      - name: Check out Code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
      - name: Login to Harbor
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: ${{ secrets.HARBOR_USER }}
          password: ${{ secrets.HARBOR_TOKEN }}
      - name: Build Image Harbor
        uses: docker/build-push-action@v2
        with:
          platforms: "linux/amd64"
          push: true
          # To disable the unnecessary git pull
          context: .
          build-args: |
            GIT_HASH=${{ github.sha }}
            GIT_BRANCH=${{ github.ref }}
          tags: |
            hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}-amd64
  multi-arch:
    name: Create Multi-Architecture Image
    runs-on: [amd64, cd]
    needs: ["build-arm64", "build-amd64"]
    env:
      IMAGE_TAG: ${{ inputs.daily_run_image_tag || 'v7.5.0' }}
    steps:
      - name: Login to Harbor
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: ${{ secrets.HARBOR_USER }}
          password: ${{ secrets.HARBOR_TOKEN }}
      - name: create-manifest-harbor
        run: |
          docker manifest create hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }} \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}-amd64 \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}-arm64
      - name: push-manifest-harbor
        run: |
          docker manifest push hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}
      - name: Login to Harbor (keyspace)
        if: ${{ inputs.update_daily_run_image || github.ref == 'refs/heads/cloud-engine' }}
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: "robot$keyspace+cdbot"
          password: ${{ secrets.HARBOR_KEYSPACE_TOKEN }}
      - name: set-additional-tags
        if: ${{ inputs.update_daily_run_image || github.ref == 'refs/heads/cloud-engine' }}
        run: |
          # Remove the local manifest entry if it exists
          docker manifest rm hub.pingcap.net/keyspace/tikv-cse:${{ env.IMAGE_TAG }} || true
          
          docker manifest create hub.pingcap.net/keyspace/tikv-cse:${{ env.IMAGE_TAG }} \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}-amd64 \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tikv:${{ github.sha }}-arm64
          
          docker manifest push hub.pingcap.net/keyspace/tikv-cse:${{ env.IMAGE_TAG }}
