name: CI

on:
  pull_request:
  workflow_dispatch:

# Concurrency ensures that only a single job or workflow using the same concurrency group will run at a time.
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  dev:
    name: Run rustfmt and clippy
    runs-on: [amd64, ci]
    env:
      RUSTFLAGS: -Dwarnings
      FAIL_POINT: "1"
      ROCKSDB_SYS_SSE: "1"
      RUST_BACKTRACE: "1"
      LOG_LEVEL: INFO
      EXTRA_CARGO_ARGS: --no-run
      CARGO_UNSTABLE_SPARSE_REGISTRY: true
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          token: ${{ secrets.GIT_ACTION_BOT }}
      - name: Check cmake & protoc # use local to work around github rate limit
        run: |
          cmake --version
          protoc --version
      - name: Configure git for private modules
        env:
          TOKEN: ${{ secrets.GIT_ACTION_BOT }}
        run: git config --global url."https://${TOKEN}@github.com/".insteadOf "https://github.com/"
      - name: Install `rust` toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2023-12-10
          components: clippy, rustfmt
      - name: Set crates.io mirror # https://developer.aliyun.com/mirror/crates.io-index
        run: |
          mkdir -vp ${CARGO_HOME:-$HOME/.cargo}
          cat << EOF | tee ${CARGO_HOME:-$HOME/.cargo}/config.toml
          [source.crates-io]
          replace-with = 'aliyun'
          [source.aliyun]
          registry = "sparse+https://mirrors.aliyun.com/crates.io-index/"
          EOF
      - name: Check protobuf
        working-directory: ./
        run: |
          make check-protobuf
      - name: Check format
        working-directory: ./
        run: |
          cargo install -q cargo-sort --version 1.0.9 --locked
          cargo fmt --check
          cargo sort -w ./Cargo.toml ./*/Cargo.toml components/*/Cargo.toml cmd/*/Cargo.toml --check
      - name: Run clippy
        working-directory: ./
        run: |
          make clippy
