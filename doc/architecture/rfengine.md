# RfEngine: Design and Architecture

> [!NOTE]
> Generated by AI. Some details may be incorrect or outdated.
> Reviewed by @coocood.

## 1. Overview

`rfengine` is a specialized, high-performance, log-structured storage engine designed exclusively for persisting Raft logs and their associated state for multiple Raft groups (Peers). It is a core component of TiKV's storage layer, optimized for the sequential, append-only write patterns characteristic of Raft consensus.

Its primary design goals are:

- **High Throughput & Low Latency:** Achieved by batching writes and appending them sequentially to a Write-Ahead Log (WAL), minimizing random disk I/O.
- **Durability:** All state changes are persisted to the WAL before being acknowledged, ensuring no data loss on restart.
- **Efficient Garbage Collection:** A background compaction process cleans up obsolete log entries and reorganizes data for efficient long-term storage and reads.
- **Cloud-Native Backups:** Integrated support for lightweight, continuous backups to a Distributed File System (DFS) like S3, enabling Point-in-Time Recovery (PITR).

## 2. Core Concepts

Understanding `rfengine` requires familiarity with its key data structures and on-disk artifacts.

- **Epoch:** The fundamental unit of log rotation. The engine's life is divided into epochs, each represented by an incrementing integer (`u32`). Each epoch has its own WAL file. This mechanism simplifies file management and garbage collection.

- **Write-Ahead Log (WAL):** The heart of the write path. All incoming data (new Raft entries, state changes) is batched and appended to a single file for the current epoch, named `{epoch_idx}.wal`. This ensures that writes are sequential and fast. The WAL file is pre-allocated and recycled to avoid filesystem allocation overhead.

- **Raft Log File (`.rlog`):** While the WAL is a temporary, mixed log for all peers, `.rlog` files are the permanent, compacted storage for a single peer's Raft entries. After a WAL file is filled and rotated, a background process compacts it, splitting the logs for each peer into dedicated files named `{peer_id}_{first_index}_{last_index}.rlog`.

- **Manifest:** A journaled metadata file (`MANIFEST`) that serves as the source of truth for the engine's state. It records which `.rlog` files exist, what their log ranges are, and the latest state for every peer. It is a log-structured file itself, where each entry is a `ChangeSet` that describes the transition from one state to the next.

- **WAL Chunk:** For the lightweight backup feature, a segment of a WAL file that is uploaded to a DFS. This allows for continuous streaming of Raft logs to remote storage.

## 3. Architecture

`rfengine` employs a multi-threaded architecture to separate the fast write path from slower background maintenance tasks like compaction and backups.

```
+------------------+
|   RfEngineCore   | <--- (Write/Read API Calls)
| (In-Memory State)|
| - peers (DashMap)|
| - writer (Mutex) |
+------------------+
        |
        | 1. Write/Apply (In-Memory)
        | 2. Persist (Sync I/O)
        |
+------------------+
|    WalWriter     | --> Writes to --> [ {epoch}.wal ] (Sync WAL on fast disk)
+------------------+
        |
        | 3. Send Tasks (Rotate, Backup, etc.)
        |
+------------------+
|  ServiceWorker   | (Background Task Coordinator)
+------------------+
   |            |
   | (Async     | 4a. (Compaction)
   |  Write)    |
   v            v
+----------+  +-----------------+
|Async WAL |  |  CompactWorker  | --> Reads <-- [ {epoch-1}.wal ]
|Writer    |  +-----------------+       |
+----------+          |               |
   |                  | 5. Writes     |
   | 4b. (DFS Sync)   v               v
   v            +-----------+   +-----------------+
+----------+    | MANIFEST  |   |  {peer}.rlog    |
|DfsWorker |    +-----------+   +-----------------+
+----------+
   |
   | 5b. (Upload)
   v
+----------+
|   DFS    | (e.g., S3)
| (Remote) |
+----------+
```

### Components:

1.  **`RfEngineCore` (`engine.rs`):**

    - The main struct and public API endpoint.
    - Holds the primary in-memory state: a `dashmap::DashMap` of `peer_id -> PeerData`. `PeerData` contains the Raft logs (`RaftLogs`) and key-value states for a single peer, enabling extremely fast reads for recent entries.
    - Owns the `WalWriter` and `Sender` channels to the background workers.

2.  **`WalWriter` (`writer.rs`):**

    - Manages all writes to the current WAL file.
    - Uses a `DmaBuffer` to ensure writes are memory-aligned for high-performance direct I/O.
    - Batches multiple `PeerBatch` modifications into a single compressed block.
    - Handles WAL file rotation when the current file exceeds `target-file-size` or a day has passed. Rotation is a critical step that triggers compaction.

3.  **`ServiceWorker` (`service_worker.rs`):**

    - A central background thread that orchestrates all other workers.
    - Receives tasks (`ServiceTask`) from `RfEngineCore`, such as `Rotate`, `Backup`, and `Write` (for the async path).
    - Dispatches tasks to the `CompactWorker` and `DfsWorker`.

4.  **`CompactWorker` (`compact_worker.rs`):**

    - A dedicated thread for WAL compaction.
    - When triggered, it reads a completed WAL file (`{epoch-1}.wal`), aggregates all data per peer, and writes out the structured `.rlog` files.
    - It then updates the `MANIFEST` with the new state. This process effectively garbage collects the WAL file.
    - Also responsible for handling "heavy" backups and periodic snapshots for the lightweight backup feature.

5.  **`DfsWorker` (`dfs_worker.rs`):**
    - Also known as `ObjectStorageWorker`, this component enables the lightweight backup feature.
    - It runs in its own thread and tails an _asynchronous_ WAL file.
    - As new data is written to the async WAL, the `DfsWorker` reads it and uploads it in chunks (`WAL Chunks`) to a configured DFS (e.g., S3).
    - This provides a continuous, low-overhead stream of Raft logs to remote storage.

## 4. Key Workflows

### Write Path

1.  A `WriteBatch` containing modifications for one or more peers is submitted to `RfEngine::write`.
2.  **Apply Phase:** `RfEngine` first applies the batch to its in-memory `peers` map. This is a fast, lock-free (due to DashMap) operation that makes the new data immediately available for reads.
3.  **Persist Phase:** The `WriteBatch` is passed to the `WalWriter`.
4.  The `WalWriter` serializes the batch, compresses it if it exceeds `batch-compression-threshold`, and writes it to the current WAL file using aligned direct I/O. This is the primary durability point.
5.  If the write causes the WAL to exceed its target size, the `WalWriter` rotates to a new epoch and sends a `Rotate` task to the `ServiceWorker` to schedule compaction of the old WAL.

### WAL Compaction and GC

1.  After a WAL file for `epoch_id` is filled, the `WalWriter` rotates, creating a new file for `epoch_id + 1`. It then sends a `Rotate { epoch_id }` task.
2.  The `ServiceWorker` receives this and dispatches a `Compact { epoch_id }` task to the `CompactWorker`.
3.  The `CompactWorker` reads the entire `{epoch_id}.wal` file from start to finish.
4.  It reconstructs the state changes and log entries for every peer mentioned in that WAL.
5.  For each peer, it writes its Raft logs into one or more new `.rlog` files.
6.  It generates a `ChangeSet` containing all state updates and the list of newly created `.rlog` files.
7.  This `ChangeSet` is appended to the `MANIFEST` file and synced to disk.
8.  Once the `MANIFEST` is updated, the `CompactWorker` proceeds to delete the now-obsolete `.rlog` files that were superseded by the peer's new `truncated_idx`.

### Lightweight Backup and Restore

1.  **Backup:**
    - When enabled, a second, asynchronous WAL path is active.
    - The `DfsWorker` tails this async WAL. As data is flushed, it reads the new bytes and uploads them as a `WAL Chunk` object to S3. The object key contains the epoch and offset range (e.g., `store_backup/{id}/wal_chunks/e{epoch}_{start_off}_{end_off}.wal`).
    - Periodically (every `EPOCH_SNAPSHOT_LEN` epochs), the `CompactWorker` creates a consistent snapshot. It gathers all current peer states and `.rlog` files, bundles them into two objects (`.meta` and `.rlog`), and uploads them to S3.
2.  **Restore:**
    - To restore to a point in time, the system fetches the latest snapshot from before the target time.
    - It restores the `MANIFEST` and `.rlog` files from the snapshot.
    - It then fetches all subsequent `WAL Chunk` files from S3 and replays them in order on top of the restored state, bringing the engine to the desired point in time.

## 5. On-Disk Layout

A typical `rfengine` directory contains:

```
/path/to/rfengine/
├── 0.wal             # Recycled WAL file for epoch N
├── 1.wal             # Recycled WAL file for epoch N+1
├── 2.wal             # Recycled WAL file for epoch N+2
├── 3.wal             # Recycled WAL file for epoch N+3
├── LOCK              # Directory lock file
├── MANIFEST          # Metadata journal
└── 0000000000000001_0000000000000005_0000000000000120.rlog # Compacted logs
```

- **`.wal` files:** There are always 4 WAL files, used in a round-robin fashion based on `epoch_id % 4`.
- **`MANIFEST`:** The single source of truth for recovery.
- **`.rlog` files:** The compacted, long-term storage for Raft logs.

If a separate `wal_sync_dir` is configured, the `.wal` files will reside there for better I/O isolation.

## 6. Configuration (`config.rs`)

Key configuration parameters include:

- `target-file-size`: The size at which a WAL file is rotated (e.g., "512MB").
- `batch-compression-threshold`: Batches larger than this are compressed with LZ4 before being written to the WAL (e.g., "8KB").
- `wal-sync-dir`: An optional separate directory for WAL files to isolate sequential write I/O from random read I/O. Enabling this also activates the async WAL path.
- `lightweight-backup`: A boolean to enable the DFS/S3 backup feature.
- `rlog-cache-capacity`: When lightweight backup is on, this controls the in-memory cache for `.rlog` files to speed up snapshot creation.
- `compact-wal-sync-concurrency`: The number of threads to use for syncing `.rlog` files to disk during compaction.

## 7. Usage and API

While `rfengine` implements the `engine_traits::RaftEngine` trait, many of its methods are stubbed with `panic!()`. The primary interaction with the engine is through its native, concrete methods defined in `RfEngineCore`.

**Core Write API:**

- `fn write(&self, wb: WriteBatch) -> Result<usize>`: The main entry point for all modifications. It applies the batch to memory and then persists it to the WAL.

**Core Read API:**

- `fn get_raft_entry(&self, peer_id: u64, index: u64) -> Option<Entry>`: Fetches a single Raft entry for a peer.
- `fn fetch_raft_entries_to(...)`: Fetches a range of entries for a peer.
- `fn get_state(&self, peer_id: u64, key: &[u8]) -> Option<Bytes>`: Retrieves a key-value state for a peer.

**Backup API:**

- `fn backup(&self, task: BackupTask)`: Initiates a backup process, which is handled by the appropriate background worker.

The `WriteBatch` object is the primary way to bundle changes. It allows for appending logs, truncating logs, and setting key-value states for multiple peers in a single atomic write.
