# KvEngine: Design and Architecture

> [!NOTE]
> Generated by AI. Some details may be incorrect or outdated.
> Reviewed by @breezewish @coocood.

## 1. Introduction

`kvengine` is for storing actual user data (the "state machine" role in Raft). It uses a Log-Structured Merge-Tree (LSM-Tree) architecture similar to RocksDB and features a pluggable Distributed File System (DFS) layer, making it ideal for environments using object storage like AWS S3.

## 2. Architecture Overview

The engine is built around a sharded, multi-level LSM-Tree. Each shard is an independent LSM-Tree responsible for a contiguous key range, enabling high concurrency. `kvengine` is used as the underlying storage engine (for storing user data) for `rfstore`, which is the Raft layer responsible for replication and consensus.

### Core Components

- **rfstore**: The Raft layer that manages Raft groups (peers). It handles Raft messages, proposes commands, and ensures data consistency across replicas. It calls `kvengine` for storing actual user data.
- **Engine/EngineCore**: Manages shards within `kvengine`, background workers (flush, compaction), and provides the main storage API to `rfstore`.
- **Shard**: An independent LSM-Tree for a key range. It's the unit of concurrency and data partitioning within `kvengine`. Each Raft group in `rfstore` corresponds to a shard in `kvengine`.
- **DFS (Distributed File System)**: An abstraction for storage backends, with `S3Fs` as the primary implementation.

```ascii
+----------------------------------------------+
|                   rfstore                    |
|       (Raft Consensus and Replication)       |
|                                              |
|  +-----------+   +-----------+   +-----------+
|  | Region 1  |   | Region 2  |   | Region N  |
|  +-----------+   +-----------+   +-----------+
|        |               |               |     |
+--------|---------------|---------------|-----+
         |               |               |
         v               v               v
+----------------------------------------------+
|                   kvengine                   |
|              (Storage Engine)                |
|                                              |
|  +-----------+   +-----------+   +-----------+
|  |  Shard 1  |   |  Shard 2  |   |  Shard N  |
|  | [A -> D)  |   | [D -> G)  |   | [X -> Z)  |
|  +-----------+   +-----------+   +-----------+
|        |               |              |      |
|    LSM-Tree        LSM-Tree       LSM-Tree   |
|                                              |
+----------------------------------------------+
```

### Overview of Row, Columnar, and Vector Storage Layers

KvEngine supports multiple storage formats optimized for different workloads:

1.  **Row Storage (LSM-Tree)**: The primary storage for transactional (OLTP) data. It's based on an LSM-Tree structure, which is highly efficient for write-intensive workloads.

    - **MemTable**: In-memory buffer for recent writes.
    - **L0Table**: Files flushed from the MemTable, stored on DFS.
    - **SsTable**: Compacted, sorted files at higher levels (L1, L2, etc.) on DFS.
    - **BlobTable**: Stores large values separately to reduce write amplification in the main LSM-Tree.

2.  **Columnar Storage**: Optimized for analytical (OLAP) queries. Data is organized by column rather than by row, allowing for efficient scanning and aggregation of specific columns. Columnar files are generated from the row-oriented data in the LSM-Tree.

3.  **Vector Storage**: Designed for similarity searches on high-dimensional vector data (embeddings). Vector indexes are built from columnar data and leverage libraries like `usearch` for fast approximate nearest neighbor (ANN) search.

## 3. LSM-Tree Hierarchy

`kvengine`'s LSM-Tree is structured into multiple levels to manage data from recent writes to long-term storage.

```ascii
  Writes
    |
    v
+----------------------+   flush   +-------------------------+
| MemTable (In-Memory) |---------> |   L0 SSTables (on DFS)  |
| (sorted skiplist)    |           | (key ranges can overlap)|
+----------------------+           +-------------------------+
                                             |
                                             | compaction
                                             v
                                   +-------------------------+
                                   |   L1 SSTables (on DFS)  |
                                   | (sorted, non-overlapping) |
                                   +-------------------------+
                                             |
                                             | compaction
                                             v
                                   +-------------------------+
                                   |   ...                   |
                                   +-------------------------+
                                             |
                                             | compaction
                                             v
                                   +-------------------------+
                                   |   Ln SSTables (on DFS)  |
                                   | (sorted, non-overlapping) |
                                   +-------------------------+
```

## 4. Core Processes

### Write Path

The write path is designed to be fast, buffering writes in memory before flushing them to persistent storage. All writes must go through the Raft consensus protocol to ensure consistency.

#### Request Handling in rfstore and kvengine

1.  A write request (Put/Delete) arrives at the `rfstore` layer for the leader peer of a Raft group.
2.  `rfstore` proposes the command to the Raft group.
3.  Once the command is committed by the Raft group, `rfstore` applies the command.
4.  During application, `rfstore` calls `kvengine`'s `write` method, passing a `WriteBatch`.
5.  The `Engine` in `kvengine` identifies the correct `Shard` based on the key.
6.  The `WriteBatch` is applied to the `Shard`'s active **MemTable**.

```ascii
Write Request -> rfstore (Leader Peer) -> Propose to Raft Group
                                             | (Committed)
                                             v
                                    rfstore Apply -> kvengine::write()
                                             |
                                             v
                                    Shard -> Active MemTable (SkipList)
                                             |
                                             +-- (MemTable full) --> Immutable MemTable -> Flush Queue
```

#### In-Memory Buffering: The MemTable (SkipList Implementation)

- The `Shard` writes the data into its active **MemTable**.
- The MemTable is implemented as a **SkipList**, which provides efficient in-memory insertion and sorted iteration.
- When the active MemTable becomes full, it is made immutable and a new one is created to handle subsequent writes. The immutable MemTable is then queued for flushing.

#### Transaction Handling and MVCC

KvEngine supports Multi-Version Concurrency Control (MVCC). Each key-value pair is associated with timestamps.

- **`start_ts`**: The start timestamp of the transaction that wrote the value.
- **`commit_ts`**: The commit timestamp of the transaction.

A write operation doesn't overwrite the old value but instead inserts a new version with its corresponding timestamps. This allows for snapshot isolation and consistent reads. The actual value stored in the `Write` CF includes the `commit_ts`, while the `start_ts` is part of the user-facing key encoding.

1.  A write operation (e.g., a prewrite in a 2PC transaction) is proposed through `rfstore` and associated with a `start_ts`.
2.  Once committed, the key is written to the `LOCK` column family (CF) in the `kvengine` shard with the `start_ts` and other transaction metadata.
3.  Upon commit, another proposal is made. Once committed, `rfstore` instructs `kvengine` to look up the `start_ts`, write the data to the `WRITE` CF with a `commit_ts`, and remove the lock from the `LOCK` CF. The `UserMeta` field in the value stores both `start_ts` and `commit_ts`.

```ascii
      Prewrite (start_ts)                Commit (commit_ts)
           |                                    |
           v                                    v
 rfstore Propose & Commit             rfstore Propose & Commit
           |                                    |
           v                                    v
+-----------------------+           +-----------------------+
| Write to LOCK CF      |           | Write to WRITE CF     |
| (in MemTable)         |           | (in MemTable)         |
| key -> lock_info      |           | key+commit_ts -> data |
+-----------------------+           +-----------------------+
                                              |
                                              v
                                    +-----------------------+
                                    | Delete from LOCK CF   |
                                    +-----------------------+
```

### Read Path

The read path must consolidate data from multiple sources to provide a consistent view of the data at a specific timestamp.

#### Point Lookups and Range Scans

- **Point Lookups**: Retrieve the value for a specific key at a given timestamp.
- **Range Scans**: Iterate over a range of keys at a given timestamp.

#### Merging Data from MemTable, L0Table, and Ln-SSTables

To satisfy a read request, KvEngine searches for the correct version of the key by looking through the storage layers in a specific order:

1.  **MemTables** (from newest to oldest)
2.  **L0Tables** (from newest to oldest)
3.  **Ln-SSTables** (from L1 to the highest level)

The search stops as soon as the first version of the key with a `commit_ts` less than or equal to the read timestamp is found.

```ascii
Read Request (Key, Timestamp)
      |
      v
+-----------------+
|   MemTables     | -> Found? -> Return
+-----------------+
      | (Not Found)
      v
+-----------------+
|   L0Tables      | -> Found? -> Return
+-----------------+
      | (Not Found)
      v
+-----------------+
|   L1 SSTables   | -> Found? -> Return
+-----------------+
      | (Not Found)
      v
+-----------------+
|   ...           |
+-----------------+
      | (Not Found)
      v
+-----------------+
|   Ln SSTables   | -> Found? -> Return
+-----------------+
      | (Not Found)
      v
  (Key Not Found)
```

#### Iterator Architecture: ConcatIterator and MergeIterator

To efficiently scan over multiple sorted files and in-memory structures, KvEngine uses a hierarchical iterator model:

- **`TableIterator`**: Iterates over a single SSTable.
- **`ConcatIterator`**: Concatenates multiple `TableIterator`s for a single level (e.g., all SSTables in L1) into a single sorted sequence.
- **`MergeIterator`**: Merges multiple sorted iterators (like a `ConcatIterator` for each level and a `MemTable` iterator) into a final, globally sorted view. This is a key component for implementing the LSM-Tree read logic.

#### Blob Value Handling

For large values stored in `BlobTable`s, the main LSM-Tree only contains a reference (`BlobRef`) to the value. During a read:

1.  The `BlobRef` is retrieved from the SSTable.
2.  The engine uses the `BlobRef` (which contains the file ID and offset) to fetch the actual value from the corresponding `BlobTable` on DFS.
3.  A `BlobPrefetcher` can be used to proactively fetch blob values to reduce latency.

### Flush Process

The flush process moves data from the in-memory `MemTable` to persistent L0 `SSTables` on the DFS. This process is managed by `kvengine` but coordinated through `rfstore` to maintain consistency.

```ascii
           Write
             |
             v
+-----------------------+   is full   +------------------------+
| Writable MemTable     |-----------> | Immutable MemTable     |
| (accepting new writes)|             | (queued for flushing)  |
+-----------------------+             +------------------------+
                                                 |
                                                 v
                                        +----------------+
                                        | Flush Worker   |
                                        | (in kvengine)  |
                                        +----------------+
                                                 |
                                                 v
                                      +--------------------+
                                      | sstable::L0Builder |
                                      | (builds L0 file)   |
                                      +--------------------+
                                                 |
                                                 v
                                      +--------------------+
                                      | L0 SSTable on DFS  |
                                      +--------------------+
                                                 |
                                                 v
                                      +--------------------+
                                      |   ChangeSet        |
                                      +--------------------+
                                                 |
                                                 v
                                      rfstore Propose & Commit
                                                 |
                                                 v
                                      kvengine Apply ChangeSet
```

#### Triggering Conditions for a Flush

A flush is triggered when:

- The active MemTable reaches its size limit (`max_mem_table_size`).
- A manual flush is requested.
- A shard split or merge operation occurs.

#### Process of Flushing MemTable to L0Table on DFS

1.  The active `MemTable` is marked as immutable.
2.  A new `MemTable` is created for new writes.
3.  The immutable `MemTable` is added to a flush queue within `kvengine`.
4.  A background **flush worker** in `kvengine` picks up the task.
5.  The worker iterates over the `MemTable`'s contents and builds one or more **L0Tables**.
6.  The newly created L0Table files are written to the DFS.
7.  A `ChangeSet` containing metadata about the new L0Table(s) is generated.
8.  This `ChangeSet` is sent to `rfstore`, which proposes it to the Raft group.
9.  Once the `ChangeSet` is committed, `rfstore` instructs all replicas' `kvengine` instances to apply it, updating their shard metadata.
10. The old MemTable is freed from memory.

### Compaction Workflows

Compaction is essential for LSM-Trees to reduce read amplification, remove deleted or obsolete data, and maintain the tree structure. Like flushing, compaction is managed by `kvengine` and coordinated through `rfstore`.

#### LSM-Tree Compaction (Row-oriented)

This is the standard compaction process for row-oriented data.

- **L0 to L1 Compaction**: Merges one or more L0Tables with overlapping L1 SSTables to produce new L1 SSTables. Since L0Tables can have overlapping key ranges, this process is crucial.
- **Ln to Ln+1 Compaction**: Merges one or more SSTables from level `n` with overlapping SSTables from level `n+1`. The key ranges within Ln (for n > 0) are non-overlapping.
- **Major Compaction**: A special compaction that merges all files in the LSM-Tree into a single level, reclaiming the maximum amount of space.

The general workflow for compaction is:

1. A background **compaction worker** in `kvengine` selects files for compaction.
2. It performs the compaction, reading input files from DFS and writing new files to DFS.
3. It generates a `ChangeSet` describing the changes (files created and deleted).
4. The `ChangeSet` is proposed through `rfstore` to the Raft group.
5. Once committed, all replicas apply the `ChangeSet` to their `kvengine` shard metadata.

#### Columnar File Generation & Compaction

- **L0 SSTable to Columnar File Conversion**: A specialized compaction task reads row-oriented L0Tables and converts them into a columnar format. This is the entry point for data into the analytical storage layer.
- **Columnar Compaction (L0 -> L1, L1 -> L2)**: Columnar files also have their own compaction lifecycle to merge smaller files into larger ones, improving scan efficiency.

#### Vector Index Generation

- Vector indexes are built from data in the columnar files.
- When new columnar data is generated, a background task can be triggered to read the vector columns and update the corresponding vector index files on DFS.

#### Remote Compaction

To offload resource-intensive compaction work, `kvengine` can delegate compaction tasks to a remote service. The process is similar to local compaction, but the actual file merging happens on a separate service.

```ascii
+-----------------------------+                              +----------------------------+
| kvengine                    |                              | Remote Compactor Service   |
+-----------------------------+                              +----------------------------+
              |                                                              |
              | 1. Select files for compaction                               |
              | 2. Create CompactionRequest (JSON)                           |
              |                                                              |
              |---------------------- POST /compact -----------------------> |
              |                                                              | 3. Receive request
              |                                                              | 4. Download files from DFS
              |                                                              | 5. Perform compaction
              |                                                              | 6. Upload new files to DFS
              |                                                              | 7. Return ChangeSet (Protobuf)
              | <---------------------- 200 <USER> <GROUP>|
              |                                                              |
              | 8. Propose ChangeSet via rfstore                             |
              | 9. Apply ChangeSet to update shard metadata                  |
              |                                                              |

(If the remote call fails, kvengine can fall back to performing the compaction locally.)
```

This remote compaction model is crucial for cloud deployments, as it allows for independent scaling of compute resources for the database and for maintenance tasks.

## 5. File Layout References

### SSTable File Layout

An SSTable file contains multiple data blocks, index blocks, properties, and a footer. This structure allows for efficient lookups without reading the entire file.

```ascii
+-----------------------------------+
| Data Block 1                      |
+-----------------------------------+
| Data Block 2                      |
+-----------------------------------+
| ...                               |
+-----------------------------------+
| Data Block N                      |
+-----------------------------------+
| Old Version Data Blocks (Optional)|
+-----------------------------------+
| Index Block (for latest versions) |
+-----------------------------------+
| Old Index Block (for old versions)|
+-----------------------------------+
| Auxiliary Index (BinaryFuse8 Filter)|
+-----------------------------------+
| Properties (min/max key, ts, etc.)|
+-----------------------------------+
| Footer (offsets, magic number)    |
+-----------------------------------+
```

- **Data Blocks**: Store sorted key-value pairs. They are typically compressed.
- **Index Blocks**: Contain pointers to the data blocks, allowing for fast lookups. Separate indexes exist for the latest and older versions of data.
- **Auxiliary Index**: A `BinaryFuse8` filter is used to quickly determine if a key is likely to be in the table, reducing unnecessary block reads.
- **Properties**: Stores metadata about the table, such as the smallest and biggest keys, max timestamp, and number of entries.
- **Footer**: Contains offsets to the other sections of the file and a magic number for validation.

### Columnar File Layout

For analytical workloads, `kvengine` can store data in a columnar format. A single columnar file can contain data for multiple TiDB tables, and each table's data is organized by column.

```ascii
+-----------------------------------+
| Table 1: Handle Column Packs      |
+-----------------------------------+
| Table 1: Version Column Packs     |
+-----------------------------------+
| Table 1: Column A Packs           |
+-----------------------------------+
| ...                               |
+-----------------------------------+
| Table N: Column X Packs           |
+-----------------------------------+
| Table 1 Metadata (Index)          |
+-----------------------------------+
| ...                               |
+-----------------------------------+
| Table N Metadata (Index)          |
+-----------------------------------+
| File Properties (min/max key, etc)|
+-----------------------------------+
| Table Offsets                     |
+-----------------------------------+
| Footer (magic, offsets)           |
+-----------------------------------+
```

- **Packs**: A pack is the basic unit of storage within a column, containing a compressed batch of values (e.g., 8192 rows).
- **Handle & Version Columns**: Special columns that store the row handle (primary key) and MVCC version information.
- **Table Metadata**: Contains information for each table in the file, including column metadata and an index to locate packs for each column.
- **Table Offsets**: Pointers to the metadata section for each table within the file.

### Vector Index Layout

`kvengine` supports vector similarity search using specialized index files. The layout is optimized for loading the index structure and associated metadata efficiently.

```ascii
+-----------------------------------+
| usearch Index Data                |
+-----------------------------------+
| Versions (u64 array)              |
+-----------------------------------+
| Handles (int or common)           |
+-----------------------------------+
| Nulls Bitmap (v2+)                |
+-----------------------------------+
| Properties (table/col id, etc.)   |
+-----------------------------------+
| Footer (offsets, magic)           |
+-----------------------------------+
```

- **usearch Index Data**: The serialized index data from the `usearch` library, containing the vector graph structure.
- **Versions**: An array of MVCC timestamps corresponding to each vector in the index.
- **Handles**: An array of row handles (primary keys) corresponding to each vector.
- **Nulls Bitmap**: A bitmap indicating which vectors are MVCC tombstones (deletions).
- **Properties**: Metadata such as table ID, column ID, and distance metric used.

## 6. Shard Management

Shard management operations like split and merge are critical for load balancing and scalability. These operations are coordinated by `rfstore` and executed by `kvengine`.

### Shard Split

When a shard grows too large, it can be split into two or more smaller shards.

```ascii
Before Split:
+-----------------------------------------------------+
| Shard A [start_key, end_key)                        |
+-----------------------------------------------------+

After Split (on split_key):
+-----------------------------+ +-----------------------------+
| Shard B [start_key, split_key) | | Shard C [split_key, end_key) |
+-----------------------------+ +-----------------------------+
```

Process:

1.  `rfstore` initiates a split, typically triggered by statistics reported by `kvengine` (e.g., size or key count).
2.  `rfstore` proposes a `split` command to the Raft group.
3.  Once committed, `rfstore` instructs `kvengine` to perform the split.
4.  In `kvengine`, the parent shard (`A`) is updated to a new version, and its MemTable is switched.
5.  New child shards (`B`, `C`) are created with the new key ranges.
6.  The child shards initially share the parent shard's SSTables. They do not have their own files yet.
7.  An **Initial Flush** is triggered for each new shard. This process reads the data for the shard's key range from the shared parent files and its own MemTables, and writes a new set of L0 SSTables for the shard. This operation's result (`ChangeSet`) is also replicated via Raft.
8.  Once the initial flush is complete, the shard is independent and no longer relies on the parent's files.

### Shard Merge

Two adjacent shards can be merged into a single larger shard.

```ascii
Before Merge:
+-----------------------------+ +-----------------------------+
| Shard X [start, mid)        | | Shard Y [mid, end)          |
+-----------------------------+ +-----------------------------+

After Merge:
+-----------------------------------------------------+
| Shard Z [start, end)                                |
+-----------------------------------------------------+
```

Process:

1.  `rfstore` initiates a merge, typically triggered by PD.
2.  A `PrepareMerge` command is proposed and committed. `rfstore` then instructs both source shards (`X`, `Y`) in `kvengine` to prepare for merging, which involves flushing their MemTables.
3.  `rfstore` then proposes a `CommitMerge` command.
4.  Once committed, `rfstore` instructs `kvengine` to create a new shard (`Z`) with the combined key range.
5.  The metadata of the two source shards are merged. This includes combining their lists of SSTables, columnar files, etc.
6.  The new shard `Z` takes ownership of all files from both source shards.
7.  The source shards are removed.

## 7. Advanced Features

### Infrequent Access (IA) Storage Tier

- **`IaFile` and `IaAutoFile`**: These are special file handles for data that is expected to be accessed infrequently. Instead of keeping the entire file on local disk, only the metadata (index) is cached locally. Data blocks are fetched from DFS on demand.
- **Mechanism for Data Tier Transition**: `IaAutoFile` can automatically transition data between a local "hot" tier and a remote "cold" (IA) tier based on access patterns and age, optimizing local disk usage.

### MVCC Implementation

- As described in the Write Path, MVCC is implemented by storing multiple versions of a key, each with a `commit_ts`.
- Reads use a timestamp to select the correct visible version.
- Garbage collection is performed during compaction, where versions older than a specified safe point (GC safe point) are discarded.
